import React, { useState } from 'react';
// Import only what we actually use
import { UserResponseDto, CreateUserDto } from '@my-nx/common';

// Import heavy utilities but DON'T use them - they should be tree-shaken
import {
  generateComplexFakeData,
  generateFakeEcommerceData,
  generateFakeDatabaseRecords,
  generateFakeAnimalFoodData,
  generateComprehensiveFakeData,
} from '@my-nx/common';

export function TreeShakingTest() {
  const [testMode, setTestMode] = useState<'light' | 'heavy'>('light');
  const [fakeData, setFakeData] = useState<any>(null);

  // Only use the DTOs in light mode
  const createUser = (): CreateUserDto => {
    const dto = new CreateUserDto();
    dto.name = 'Test User';
    dto.email = '<EMAIL>';
    dto.age = 25;
    return dto;
  };

  const createUserResponse = (): UserResponseDto => {
    const dto = new UserResponseDto();
    dto.id = '123e4567-e89b-12d3-a456-426614174000';
    dto.name = 'Test User';
    dto.email = '<EMAIL>';
    dto.age = 25;
    dto.createdAt = new Date();
    return dto;
  };

  const handleHeavyTest = () => {
    // This will actually use the heavy functions to demonstrate runtime imports
    console.log('🚀 Generating heavy fake data...');

    const complexData = generateComplexFakeData(3);
    const ecommerceData = generateFakeEcommerceData(2);

    setFakeData({
      complex: complexData,
      ecommerce: ecommerceData,
    });

    console.log('✅ Heavy data generated:', {
      complexCount: complexData.users.length,
      ecommerceCount: ecommerceData.products.length,
    });
  };

  // NOTE: We import the heavy utilities above but DON'T call them in light mode
  // This tests if tree-shaking properly removes unused imports
  // If tree-shaking works, faker should not be in the bundle

  return (
    <div style={{ padding: '20px', border: '1px solid #ccc', margin: '10px' }}>
      <h3>Tree-Shaking Test Component</h3>
      <p>This component imports heavy utilities from @my-nx/common.</p>
      <p>Use the buttons below to test different scenarios:</p>

      <div style={{ marginBottom: '20px' }}>
        <button
          onClick={() => setTestMode('light')}
          style={{
            marginRight: '10px',
            padding: '8px 16px',
            backgroundColor: testMode === 'light' ? '#007bff' : '#f8f9fa',
            color: testMode === 'light' ? 'white' : 'black',
            border: '1px solid #ccc',
            cursor: 'pointer',
          }}
        >
          Light Mode (Tree-shakeable)
        </button>
        <button
          onClick={() => {
            setTestMode('heavy');
            handleHeavyTest();
          }}
          style={{
            padding: '8px 16px',
            backgroundColor: testMode === 'heavy' ? '#dc3545' : '#f8f9fa',
            color: testMode === 'heavy' ? 'white' : 'black',
            border: '1px solid #ccc',
            cursor: 'pointer',
          }}
        >
          Heavy Mode (Runtime imports)
        </button>
      </div>

      {testMode === 'light' && (
        <div>
          <h4>✅ Used DTOs (should be in bundle):</h4>
          <pre
            style={{ background: '#f5f5f5', padding: '10px', fontSize: '12px' }}
          >
            {JSON.stringify(createUser(), null, 2)}
          </pre>
          <pre
            style={{ background: '#f5f5f5', padding: '10px', fontSize: '12px' }}
          >
            {JSON.stringify(createUserResponse(), null, 2)}
          </pre>

          <h4>🌳 Imported but unused (should be tree-shaken):</h4>
          <ul>
            <li>generateComplexFakeData</li>
            <li>generateFakeEcommerceData</li>
            <li>generateFakeDatabaseRecords</li>
            <li>generateFakeAnimalFoodData</li>
            <li>generateComprehensiveFakeData</li>
          </ul>
          <p style={{ color: '#666', fontSize: '14px' }}>
            <strong>
              These functions and faker should NOT be in the bundle!
            </strong>
            <br />
            They are imported but never called in light mode.
          </p>
        </div>
      )}

      {testMode === 'heavy' && fakeData && (
        <div>
          <h4>🔥 Heavy Data Generated (runtime imports):</h4>
          <div
            style={{
              maxHeight: '300px',
              overflow: 'auto',
              background: '#f5f5f5',
              padding: '10px',
            }}
          >
            <h5>Complex Data ({fakeData.complex.users.length} users):</h5>
            <pre style={{ fontSize: '10px' }}>
              {JSON.stringify(fakeData.complex.users[0], null, 2)}
            </pre>

            <h5>
              E-commerce Data ({fakeData.ecommerce.products.length} products):
            </h5>
            <pre style={{ fontSize: '10px' }}>
              {JSON.stringify(fakeData.ecommerce.products[0], null, 2)}
            </pre>
          </div>
          <p style={{ color: '#666', fontSize: '12px', marginTop: '10px' }}>
            In heavy mode, the faker functions are actually called, so they will
            be included in the bundle. This demonstrates the difference between
            imported-but-unused vs imported-and-used code.
          </p>
        </div>
      )}
    </div>
  );
}
