import {
  generateComplexFakeData,
  generateFakeEcommerceData,
  generateFakeDatabaseRecords,
  generateFakeAnimalFoodData,
  generateComprehensiveFakeData,
} from './heavy-utils';

describe('Heavy Utils with Faker', () => {
  describe('generateComplexFakeData', () => {
    it('should generate complex fake user data using multiple faker modules', () => {
      const result = generateComplexFakeData(5);

      expect(result).toHaveProperty('users');
      expect(result).toHaveProperty('metadata');
      expect(result.users).toHaveLength(5);
      expect(result.metadata.count).toBe(5);
      expect(result.metadata.generator).toBe('faker-heavy-utils');

      // Check that each user has all the expected properties
      const user = result.users[0];
      expect(user).toHaveProperty('id');
      expect(user).toHaveProperty('firstName');
      expect(user).toHaveProperty('address');
      expect(user).toHaveProperty('company');
      expect(user).toHaveProperty('finance');
      expect(user.address).toHaveProperty('coordinates');
    });
  });

  describe('generateFakeEcommerceData', () => {
    it('should generate fake e-commerce data using multiple faker modules', () => {
      const result = generateFakeEcommerceData(10);

      expect(result).toHaveProperty('products');
      expect(result).toHaveProperty('summary');
      expect(result.products).toHaveLength(10);
      expect(result.summary.totalProducts).toBe(10);

      const product = result.products[0];
      expect(product).toHaveProperty('id');
      expect(product).toHaveProperty('name');
      expect(product).toHaveProperty('vehicle');
      expect(product).toHaveProperty('dates');
      expect(product).toHaveProperty('metadata');
      expect(product.vehicle).toHaveProperty('manufacturer');
      expect(product.dates).toHaveProperty('created');
    });
  });

  describe('generateFakeDatabaseRecords', () => {
    it('should generate fake database records using many faker modules', () => {
      const result = generateFakeDatabaseRecords(8);

      expect(result).toHaveProperty('records');
      expect(result).toHaveProperty('analytics');
      expect(result.records).toHaveLength(8);
      expect(result.analytics.totalRecords).toBe(8);

      const record = result.records[0];
      expect(record).toHaveProperty('content');
      expect(record).toHaveProperty('music');
      expect(record).toHaveProperty('book');
      expect(record).toHaveProperty('science');
      expect(record).toHaveProperty('system');
      expect(record).toHaveProperty('airline');
      expect(record.book).toHaveProperty('title');
      expect(record.airline).toHaveProperty('aircraftType');
    });
  });

  describe('generateFakeAnimalFoodData', () => {
    it('should generate fake animal and food data', () => {
      const result = generateFakeAnimalFoodData(6);

      expect(result).toHaveProperty('data');
      expect(result).toHaveProperty('summary');
      expect(result.data).toHaveLength(6);

      const entry = result.data[0];
      expect(entry).toHaveProperty('animal');
      expect(entry).toHaveProperty('food');
      expect(entry).toHaveProperty('hacker');
      expect(entry.animal).toHaveProperty('type');
      expect(entry.food).toHaveProperty('dish');
      expect(entry.hacker).toHaveProperty('phrase');
    });
  });

  describe('generateComprehensiveFakeData', () => {
    it('should generate comprehensive fake data using many faker modules', () => {
      const result = generateComprehensiveFakeData(4);

      expect(result).toHaveProperty('data');
      expect(result).toHaveProperty('metadata');
      expect(result.data).toHaveLength(4);
      expect(result.metadata.fakerModulesUsed).toContain('git');
      expect(result.metadata.fakerModulesUsed).toContain('phone');
      expect(result.metadata.fakerModulesUsed).toContain('database');

      const entry = result.data[0];
      expect(entry).toHaveProperty('git');
      expect(entry).toHaveProperty('phone');
      expect(entry).toHaveProperty('word');
      expect(entry).toHaveProperty('database');
      expect(entry).toHaveProperty('datatype');
      expect(entry.git).toHaveProperty('commitMessage');
      expect(entry.database).toHaveProperty('mongodbObjectId');
    });
  });
});
