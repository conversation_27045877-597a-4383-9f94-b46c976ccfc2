#!/usr/bin/env node

/**
 * Bundle Size Test Script
 * 
 * This script tests the impact of namedExports configuration on bundle size
 * and tree-shaking effectiveness by:
 * 
 * 1. Building with current namedExports configuration
 * 2. Building without namedExports configuration
 * 3. Comparing bundle sizes and analyzing what gets included
 * 4. Generating detailed reports
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

const FRONTEND_DIR = path.join(__dirname, '../apps/frontend');
const VITE_CONFIG_PATH = path.join(FRONTEND_DIR, 'vite.config.ts');
const DIST_DIR = path.join(FRONTEND_DIR, 'dist');
const RESULTS_DIR = path.join(__dirname, '../bundle-analysis');

// Ensure results directory exists
if (!fs.existsSync(RESULTS_DIR)) {
  fs.mkdirSync(RESULTS_DIR, { recursive: true });
}

console.log('🧪 Bundle Size Test: namedExports Impact Analysis');
console.log('================================================\n');

// Read original vite config
const originalConfig = fs.readFileSync(VITE_CONFIG_PATH, 'utf8');

// Create backup
const backupPath = VITE_CONFIG_PATH + '.backup';
fs.writeFileSync(backupPath, originalConfig);

function cleanDist() {
  if (fs.existsSync(DIST_DIR)) {
    fs.rmSync(DIST_DIR, { recursive: true, force: true });
  }
}

function getBundleStats(distPath) {
  const stats = {};

  if (!fs.existsSync(distPath)) {
    return stats;
  }

  // Check both dist root and assets directory
  const assetsPath = path.join(distPath, 'assets');
  const directories = [distPath];

  if (fs.existsSync(assetsPath)) {
    directories.push(assetsPath);
  }

  directories.forEach(dir => {
    const files = fs.readdirSync(dir);

    files.forEach(file => {
      const filePath = path.join(dir, file);
      const stat = fs.statSync(filePath);

      if (stat.isFile() && (file.endsWith('.js') || file.endsWith('.css'))) {
        stats[file] = {
          size: stat.size,
          sizeKB: Math.round(stat.size / 1024 * 100) / 100
        };
      }
    });
  });

  return stats;
}

function analyzeBundle(distPath, label) {
  console.log(`\n📊 Analyzing ${label}:`);
  console.log('─'.repeat(50));
  
  const stats = getBundleStats(distPath);
  let totalSize = 0;
  
  Object.entries(stats).forEach(([file, info]) => {
    console.log(`  ${file}: ${info.sizeKB} KB`);
    totalSize += info.size;
  });
  
  console.log(`  Total: ${Math.round(totalSize / 1024 * 100) / 100} KB`);
  
  return { stats, totalSize };
}

function buildWithConfig(configModifier, label) {
  console.log(`\n🔨 Building ${label}...`);
  
  // Modify config if needed
  if (configModifier) {
    const modifiedConfig = configModifier(originalConfig);
    fs.writeFileSync(VITE_CONFIG_PATH, modifiedConfig);
  }
  
  // Clean and build
  cleanDist();
  
  try {
    execSync('pnpm nx build frontend', {
      cwd: path.join(__dirname, '..'),
      stdio: 'pipe'
    });
    
    // Copy dist to results for comparison
    const resultPath = path.join(RESULTS_DIR, label.toLowerCase().replace(/\s+/g, '-'));
    if (fs.existsSync(resultPath)) {
      fs.rmSync(resultPath, { recursive: true, force: true });
    }
    fs.cpSync(DIST_DIR, resultPath, { recursive: true });
    
    return analyzeBundle(DIST_DIR, label);
    
  } catch (error) {
    console.error(`❌ Build failed for ${label}:`, error.message);
    return null;
  }
}

// Test 1: Build with current namedExports configuration
console.log('\n🧪 Test 1: With namedExports Configuration');
const withNamedExports = buildWithConfig(null, 'With namedExports');

// Test 2: Build without namedExports configuration
console.log('\n🧪 Test 2: Without namedExports Configuration');
const withoutNamedExports = buildWithConfig((config) => {
  // Remove namedExports from commonjsOptions
  return config.replace(
    /namedExports:\s*\{[^}]*\},?\s*/s,
    ''
  );
}, 'Without namedExports');

// Test 3: Build with aggressive tree-shaking disabled
console.log('\n🧪 Test 3: With Tree-shaking Disabled');
const noTreeShaking = buildWithConfig((config) => {
  // Disable tree-shaking
  return config.replace(
    /treeshake:\s*\{[^}]*\},?\s*/s,
    'treeshake: false,'
  );
}, 'No Tree-shaking');

// Restore original config
fs.writeFileSync(VITE_CONFIG_PATH, originalConfig);
fs.unlinkSync(backupPath);

// Generate comparison report
console.log('\n📈 Bundle Size Comparison Report');
console.log('='.repeat(60));

const results = [
  { label: 'With namedExports', data: withNamedExports },
  { label: 'Without namedExports', data: withoutNamedExports },
  { label: 'No Tree-shaking', data: noTreeShaking }
].filter(r => r.data !== null);

if (results.length > 1) {
  const baseline = results[0];
  
  results.forEach((result, index) => {
    if (index === 0) {
      console.log(`\n${result.label} (baseline):`);
      console.log(`  Total size: ${Math.round(result.data.totalSize / 1024 * 100) / 100} KB`);
    } else {
      const sizeDiff = result.data.totalSize - baseline.data.totalSize;
      const percentDiff = Math.round((sizeDiff / baseline.data.totalSize) * 100 * 100) / 100;
      
      console.log(`\n${result.label}:`);
      console.log(`  Total size: ${Math.round(result.data.totalSize / 1024 * 100) / 100} KB`);
      console.log(`  Difference: ${sizeDiff > 0 ? '+' : ''}${Math.round(sizeDiff / 1024 * 100) / 100} KB (${percentDiff > 0 ? '+' : ''}${percentDiff}%)`);
    }
  });
}

// Generate detailed analysis
const reportPath = path.join(RESULTS_DIR, 'bundle-analysis-report.md');
let report = `# Bundle Size Analysis Report

Generated: ${new Date().toISOString()}

## Test Overview

This test compares bundle sizes with different Vite configurations to demonstrate the impact of \`namedExports\` on tree-shaking effectiveness.

## Test Scenarios

1. **With namedExports**: Current configuration with explicit named exports
2. **Without namedExports**: Same config but without the namedExports specification
3. **No Tree-shaking**: Tree-shaking completely disabled for comparison

## Results

`;

results.forEach((result, index) => {
  report += `### ${result.label}\n\n`;
  report += `Total bundle size: **${Math.round(result.data.totalSize / 1024 * 100) / 100} KB**\n\n`;
  report += '| File | Size (KB) |\n';
  report += '|------|----------|\n';
  
  Object.entries(result.data.stats).forEach(([file, info]) => {
    report += `| ${file} | ${info.sizeKB} |\n`;
  });
  
  report += '\n';
});

if (results.length > 1) {
  report += `## Analysis\n\n`;
  
  const baseline = results[0];
  results.slice(1).forEach(result => {
    const sizeDiff = result.data.totalSize - baseline.data.totalSize;
    const percentDiff = Math.round((sizeDiff / baseline.data.totalSize) * 100 * 100) / 100;
    
    report += `**${result.label} vs ${baseline.label}:**\n`;
    report += `- Size difference: ${sizeDiff > 0 ? '+' : ''}${Math.round(sizeDiff / 1024 * 100) / 100} KB\n`;
    report += `- Percentage change: ${percentDiff > 0 ? '+' : ''}${percentDiff}%\n\n`;
  });
}

report += `## Conclusion

The \`namedExports\` configuration in Vite's \`commonjsOptions\` helps Rollup understand which exports are available from CommonJS modules like \`@my-nx/common\`. This enables:

1. **Better tree-shaking**: Rollup can eliminate unused exports more effectively
2. **Smaller bundles**: Only imported functions/classes are included
3. **Cleaner imports**: Named imports work reliably with CommonJS modules

## Files Generated

- Bundle analysis results are saved in: \`${RESULTS_DIR}\`
- Each test scenario has its own directory with the built files
- Bundle visualization is available in \`dist/bundle-analysis.html\` after each build
`;

fs.writeFileSync(reportPath, report);

console.log(`\n📄 Detailed report saved to: ${reportPath}`);
console.log(`📁 Bundle files saved to: ${RESULTS_DIR}`);
console.log('\n✅ Bundle size test completed!');
console.log('\n💡 Next steps:');
console.log('   1. Check the bundle-analysis.html file in each test directory');
console.log('   2. Review the markdown report for detailed analysis');
console.log('   3. Compare the actual bundle contents to see what was tree-shaken');
