import { Injectable, NotFoundException } from '@nestjs/common';
import {
  CreateUserDto,
  UserResponseDto,
  UpdateUserDto,
  transformToUserResponseDto,
  transformToUserResponseDtoArray,
  serializeUserResponse,
} from '@my-nx/common';

@Injectable()
export class AppService {
  private users: UserResponseDto[] = [
    {
      id: '1',
      name: '<PERSON>',
      email: '<EMAIL>',
      age: 30,
      createdAt: new Date('2023-01-01'),
    },
    {
      id: '2',
      name: '<PERSON>',
      email: '<EMAIL>',
      age: 25,
      createdAt: new Date('2023-01-02'),
    },
  ];

  getUsers(): UserResponseDto[] {
    return this.users;
  }

  getUser(id: string): UserResponseDto {
    const user = this.users.find((u) => u.id === id);
    if (!user) {
      throw new NotFoundException(`User with ID ${id} not found`);
    }
    return user;
  }

  createUser(createUserDto: CreateUserDto): UserResponseDto {
    // Create plain object with new user data
    const newUserData = {
      id: (this.users.length + 1).toString(),
      ...createUserDto,
      createdAt: new Date(),
    };

    // Transform to UserResponseDto using class-transformer
    const newUser = transformToUserResponseDto(newUserData);
    this.users.push(newUser);
    return newUser;
  }

  updateUser(id: string, updateUserDto: UpdateUserDto): UserResponseDto {
    const userIndex = this.users.findIndex((u) => u.id === id);
    if (userIndex === -1) {
      throw new NotFoundException(`User with ID ${id} not found`);
    }

    // Merge existing user data with updates
    const updatedUserData = {
      ...this.users[userIndex],
      ...updateUserDto,
    };

    // Transform to UserResponseDto using class-transformer
    const updatedUser = transformToUserResponseDto(updatedUserData);
    this.users[userIndex] = updatedUser;

    return updatedUser;
  }
}
