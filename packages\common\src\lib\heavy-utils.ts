import { faker } from '@faker-js/faker';

/**
 * Heavy utility functions that use faker extensively
 * These functions are designed to demonstrate tree-shaking scenarios
 * where large dependencies should be excluded from bundles when not used
 *
 * Faker is much larger than lodash and includes many modules:
 * - person, internet, location, company, commerce, finance
 * - vehicle, date, color, image, lorem, music, book, science
 * - system, airline, animal, food, hacker, and many more
 */

/**
 * Generate complex fake user data using many faker modules
 * This function uses multiple faker modules to create a heavy dependency
 */
export function generateComplexFakeData(count = 10): any {
  const users = [];

  for (let i = 0; i < count; i++) {
    users.push({
      // Person data
      id: faker.string.uuid(),
      firstName: faker.person.firstName(),
      lastName: faker.person.lastName(),
      fullName: faker.person.fullName(),
      email: faker.internet.email(),
      username: faker.internet.username(),
      avatar: faker.image.avatar(),

      // Address data
      address: {
        street: faker.location.streetAddress(),
        city: faker.location.city(),
        state: faker.location.state(),
        zipCode: faker.location.zipCode(),
        country: faker.location.country(),
        coordinates: {
          lat: faker.location.latitude(),
          lng: faker.location.longitude(),
        },
      },

      // Company data
      company: {
        name: faker.company.name(),
        department: faker.commerce.department(),
        jobTitle: faker.person.jobTitle(),
        catchPhrase: faker.company.catchPhrase(),
        bs: faker.company.buzzPhrase(),
      },

      // Financial data
      finance: {
        accountNumber: faker.finance.accountNumber(),
        routingNumber: faker.finance.routingNumber(),
        creditCardNumber: faker.finance.creditCardNumber(),
        iban: faker.finance.iban(),
        bitcoinAddress: faker.finance.bitcoinAddress(),
      },
    });
  }

  return {
    users,
    metadata: {
      generated: new Date(),
      count: users.length,
      generator: 'faker-heavy-utils',
    },
  };
}

/**
 * Generate fake e-commerce data using multiple faker modules
 */
export function generateFakeEcommerceData(productCount = 20): any {
  const products = [];

  for (let i = 0; i < productCount; i++) {
    products.push({
      id: faker.string.uuid(),
      name: faker.commerce.productName(),
      description: faker.commerce.productDescription(),
      price: faker.commerce.price(),
      department: faker.commerce.department(),
      material: faker.commerce.productMaterial(),
      color: faker.color.human(),

      // Vehicle data (another heavy module)
      vehicle: {
        manufacturer: faker.vehicle.manufacturer(),
        model: faker.vehicle.model(),
        type: faker.vehicle.type(),
        fuel: faker.vehicle.fuel(),
        vin: faker.vehicle.vin(),
      },

      // Date and time data
      dates: {
        created: faker.date.past(),
        updated: faker.date.recent(),
        expires: faker.date.future(),
      },

      // Random data
      metadata: {
        tags: faker.helpers.arrayElements(
          ['popular', 'sale', 'new', 'featured', 'limited'],
          3
        ),
        rating: faker.number.float({ min: 1, max: 5, fractionDigits: 1 }),
        reviews: faker.number.int({ min: 0, max: 1000 }),
        inStock: faker.datatype.boolean(),
      },
    });
  }

  return {
    products,
    summary: {
      totalProducts: products.length,
      averagePrice:
        products.reduce((sum, p) => sum + parseFloat(p.price), 0) /
        products.length,
      departments: [...new Set(products.map((p) => p.department))],
    },
  };
}

/**
 * Generate fake database records using many more faker modules
 */
export function generateFakeDatabaseRecords(recordCount = 30): any {
  const records = [];

  for (let i = 0; i < recordCount; i++) {
    records.push({
      // Database fields
      id: faker.string.uuid(),
      slug: faker.helpers.slugify(faker.lorem.words(3)),

      // Lorem ipsum data (heavy text generation)
      content: {
        title: faker.lorem.sentence(),
        paragraph: faker.lorem.paragraphs(3),
        words: faker.lorem.words(10),
        lines: faker.lorem.lines(5),
      },

      // Music data
      music: {
        genre: faker.music.genre(),
        songName: faker.music.songName(),
      },

      // Book data
      book: {
        title: faker.book.title(),
        author: faker.book.author(),
        genre: faker.book.genre(),
        publisher: faker.book.publisher(),
        series: faker.book.series(),
      },

      // Science data
      science: {
        chemicalElement: faker.science.chemicalElement(),
        unit: faker.science.unit(),
      },

      // System data
      system: {
        fileName: faker.system.fileName(),
        mimeType: faker.system.mimeType(),
        fileType: faker.system.fileType(),
        directoryPath: faker.system.directoryPath(),
      },

      // Airline data
      airline: {
        aircraftType: faker.airline.aircraftType(),
        airplane: faker.airline.airplane(),
        airport: faker.airline.airport(),
        seat: faker.airline.seat(),
      },
    });
  }

  return {
    records,
    analytics: {
      totalRecords: records.length,
      uniqueGenres: [...new Set(records.map((r) => r.music.genre))],
      averageContentLength:
        records.reduce((sum, r) => sum + r.content.paragraph.length, 0) /
        records.length,
    },
  };
}

/**
 * Generate fake animal and food data using more faker modules
 */
export function generateFakeAnimalFoodData(count = 15): any {
  const data = [];

  for (let i = 0; i < count; i++) {
    data.push({
      id: faker.string.uuid(),

      // Animal data
      animal: {
        type: faker.animal.type(),
        dog: faker.animal.dog(),
        cat: faker.animal.cat(),
        bird: faker.animal.bird(),
        fish: faker.animal.fish(),
        horse: faker.animal.horse(),
        bear: faker.animal.bear(),
        cetacean: faker.animal.cetacean(),
        cow: faker.animal.cow(),
        crocodilia: faker.animal.crocodilia(),
        insect: faker.animal.insect(),
        lion: faker.animal.lion(),
        rabbit: faker.animal.rabbit(),
        rodent: faker.animal.rodent(),
        snake: faker.animal.snake(),
      },

      // Food data
      food: {
        adjective: faker.food.adjective(),
        description: faker.food.description(),
        dish: faker.food.dish(),
        ethnicCategory: faker.food.ethnicCategory(),
        fruit: faker.food.fruit(),
        ingredient: faker.food.ingredient(),
        meat: faker.food.meat(),
        spice: faker.food.spice(),
        vegetable: faker.food.vegetable(),
      },

      // Hacker data
      hacker: {
        abbreviation: faker.hacker.abbreviation(),
        adjective: faker.hacker.adjective(),
        ingverb: faker.hacker.ingverb(),
        noun: faker.hacker.noun(),
        phrase: faker.hacker.phrase(),
        verb: faker.hacker.verb(),
      },
    });
  }

  return {
    data,
    summary: {
      totalEntries: data.length,
      uniqueAnimalTypes: [...new Set(data.map((d) => d.animal.type))],
      uniqueFoodCategories: [
        ...new Set(data.map((d) => d.food.ethnicCategory)),
      ],
    },
  };
}

/**
 * Generate comprehensive fake data using even more faker modules
 */
export function generateComprehensiveFakeData(count = 25): any {
  const data = [];

  for (let i = 0; i < count; i++) {
    data.push({
      id: faker.string.uuid(),

      // Git data
      git: {
        branch: faker.git.branch(),
        commitEntry: faker.git.commitEntry(),
        commitMessage: faker.git.commitMessage(),
        commitSha: faker.git.commitSha(),
      },

      // Phone data
      phone: {
        number: faker.phone.number(),
        imei: faker.phone.imei(),
      },

      // Word data
      word: {
        adjective: faker.word.adjective(),
        adverb: faker.word.adverb(),
        conjunction: faker.word.conjunction(),
        interjection: faker.word.interjection(),
        noun: faker.word.noun(),
        preposition: faker.word.preposition(),
        verb: faker.word.verb(),
        words: faker.word.words(5),
      },

      // Database data
      database: {
        column: faker.database.column(),
        type: faker.database.type(),
        collation: faker.database.collation(),
        engine: faker.database.engine(),
        mongodbObjectId: faker.database.mongodbObjectId(),
      },

      // Datatype data
      datatype: {
        boolean: faker.datatype.boolean(),
        float: faker.number.float(),
        hexadecimal: faker.string.hexadecimal(),
        number: faker.number.int(),
        uuid: faker.string.uuid(),
        json: JSON.stringify({
          key: faker.lorem.word(),
          value: faker.number.int(),
          active: faker.datatype.boolean(),
        }),
      },
    });
  }

  return {
    data,
    metadata: {
      totalRecords: data.length,
      generatedAt: new Date(),
      fakerModulesUsed: [
        'git',
        'phone',
        'word',
        'database',
        'datatype',
        'string',
        'helpers',
        'number',
      ],
    },
  };
}
