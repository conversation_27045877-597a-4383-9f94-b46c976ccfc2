{"name": "@my-nx/common", "version": "0.0.1", "description": "Shared DTOs and utilities for NX monorepo with tree-shaking support", "keywords": ["dto", "validation", "<PERSON><PERSON><PERSON>", "react", "tree-shaking"], "main": "./dist/index.js", "types": "./dist/index.d.ts", "sideEffects": false, "exports": {"./package.json": "./package.json", ".": {"development": "./src/index.ts", "types": "./dist/index.d.ts", "require": "./dist/index.js", "default": "./dist/index.js"}}, "files": ["dist", "!**/*.tsbuildinfo"], "scripts": {"build": "npm run build:clean && npm run build:cjs", "build:clean": "<PERSON><PERSON><PERSON> dist", "build:cjs": "tsc --project tsconfig.lib.json", "prepublishOnly": "npm run build"}, "devDependencies": {"rimraf": "^5.0.10"}, "dependencies": {"@faker-js/faker": "^9.8.0", "@nestjs/swagger": "^11.2.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "reflect-metadata": "^0.2.2", "tslib": "^2.3.0"}}