import { useState, useEffect } from 'react';
import { Route, Routes, Link } from 'react-router-dom';
import { UserResponseDto, CreateUserDto } from '@my-nx/common';
import { UserService } from '../services/userService';
import { TreeShakingTest } from '../components/TreeShakingTest';
import { DynamicStubTest } from '../components/DynamicStubTest';

export function App() {
  const [users, setUsers] = useState<UserResponseDto[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const loadUsers = async () => {
    try {
      setLoading(true);
      setError(null);
      const userData = await UserService.getUsers();
      setUsers(userData);
    } catch (err) {
      setError(
        'Failed to load users. Make sure the backend is running on port 3000.'
      );
      console.error('Error loading users:', err);
    } finally {
      setLoading(false);
    }
  };

  const createUser = async () => {
    try {
      const newUser: CreateUserDto = {
        name: 'New User',
        email: `user${Date.now()}@example.com`,
        age: 28,
      };
      await UserService.createUser(newUser);
      await loadUsers(); // Reload users
    } catch (err) {
      setError('Failed to create user');
      console.error('Error creating user:', err);
    }
  };

  useEffect(() => {
    loadUsers();
  }, []);

  return (
    <div style={{ padding: '20px' }}>
      <h1>NX Monorepo Demo - Frontend</h1>
      <p>
        This frontend uses shared DTOs from the common package and communicates
        with the NestJS backend via axios.
      </p>

      <div role="navigation" style={{ marginBottom: '20px' }}>
        <ul
          style={{
            display: 'flex',
            listStyle: 'none',
            gap: '20px',
            padding: 0,
          }}
        >
          <li>
            <Link to="/">Users</Link>
          </li>
          <li>
            <Link to="/about">About</Link>
          </li>
        </ul>
      </div>

      <Routes>
        <Route
          path="/"
          element={
            <div>
              <h2>Users Management</h2>
              <div style={{ marginBottom: '20px' }}>
                <button onClick={loadUsers} disabled={loading}>
                  {loading ? 'Loading...' : 'Refresh Users'}
                </button>
                <button onClick={createUser} style={{ marginLeft: '10px' }}>
                  Add Sample User
                </button>
              </div>

              {error && (
                <div style={{ color: 'red', marginBottom: '20px' }}>
                  {error}
                </div>
              )}

              <div>
                <h3>Users List:</h3>
                {users.length === 0 ? (
                  <p>
                    No users found.{' '}
                    {!error &&
                      'Try adding a sample user or check if the backend is running.'}
                  </p>
                ) : (
                  <ul>
                    {users.map((user) => (
                      <li
                        key={user.id}
                        style={{
                          marginBottom: '10px',
                          padding: '10px',
                          border: '1px solid #ccc',
                        }}
                      >
                        <strong>{user.name}</strong> ({user.email})
                        {user.age && <span> - Age: {user.age}</span>}
                        <br />
                        <small>
                          Created:{' '}
                          {new Date(user.createdAt).toLocaleDateString()}
                        </small>
                      </li>
                    ))}
                  </ul>
                )}
              </div>
            </div>
          }
        />
        <Route
          path="/about"
          element={
            <div>
              <h2>About This Demo</h2>
              <p>This is a demonstration of an NX monorepo with:</p>
              <ul>
                <li>✅ NX v20 with pnpm workspaces</li>
                <li>✅ Shared common package with DTOs</li>
                <li>✅ NestJS backend with TypeScript and webpack</li>
                <li>✅ Vite React frontend</li>
                <li>✅ Axios for API communication</li>
                <li>✅ NestJS Swagger for API documentation</li>
                <li>✅ Class-validator for request validation</li>
                <li>✅ Reflect-metadata for decorators</li>
                <li>✅ Tree-shaking working with faker</li>
              </ul>

              <TreeShakingTest />
              <DynamicStubTest />
              <p>
                <strong>Backend:</strong>{' '}
                <a
                  href="http://localhost:3000/api/docs"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  Swagger API Documentation
                </a>
              </p>
              <Link to="/">← Back to Users</Link>
            </div>
          }
        />
      </Routes>
    </div>
  );
}

export default App;
