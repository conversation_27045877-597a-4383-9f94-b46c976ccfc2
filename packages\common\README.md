# @my-nx/common

This library contains shared DTOs (Data Transfer Objects) and utilities for the NX monorepo, designed to be used by both frontend and backend applications.

## Features

- **DTOs with Validation**: Uses `class-validator` decorators for input validation
- **Data Transformation**: Uses `class-transformer` for converting between plain objects and class instances
- **API Documentation**: Uses `@nestjs/swagger` decorators for automatic API documentation
- **Type Safety**: Full TypeScript support with proper type definitions
- **Reflect Metadata**: Properly configured for decorator metadata support

## Dependencies

- `class-transformer`: For transforming plain objects to class instances and vice versa
- `class-validator`: For validation decorators and validation logic
- `@nestjs/swagger`: For API documentation decorators
- `reflect-metadata`: For decorator metadata support
- `tslib`: TypeScript runtime library

## Building

Run `nx build common` to build the library.

## Running unit tests

Run `nx test common` to execute the unit tests via [Jest](https://jestjs.io).

## Usage

### Basic Import

```typescript
import {
  CreateUserDto,
  UserResponseDto,
  UpdateUserDto,
  transformToUserResponseDto,
  transformAndValidate,
} from '@my-nx/common';
```

### DTOs Available

#### CreateUserDto

Used for creating new users with validation:

- `name`: string (required, trimmed)
- `email`: string (required, email format, lowercased and trimmed)
- `age`: number (optional, minimum 0, auto-converted to number)

#### UpdateUserDto

Used for updating existing users:

- `name`: string (optional, trimmed)
- `email`: string (optional, email format, lowercased and trimmed)
- `age`: number (optional, minimum 0, auto-converted to number)

#### UserResponseDto

Used for API responses:

- `id`: string (UUID format)
- `name`: string
- `email`: string
- `age`: number (optional)
- `createdAt`: Date (auto-converted to Date object)

### Transformation Utilities

#### transformAndValidate

Transform and validate any DTO:

```typescript
const userDto = await transformAndValidate(CreateUserDto, plainObject);
```

#### transformToUserResponseDto

Transform plain object to UserResponseDto:

```typescript
const userResponse = transformToUserResponseDto(plainData);
```

#### serializeUserResponse

Convert UserResponseDto to plain object for JSON:

```typescript
const plainObject = serializeUserResponse(userDto);
```

### Backend Usage Example

```typescript
import {
  CreateUserDto,
  transformToUserResponseDto,
  transformAndValidate,
} from '@my-nx/common';

@Injectable()
export class UserService {
  async createUser(userData: any): Promise<UserResponseDto> {
    // Validate and transform input
    const createUserDto = await transformAndValidate(CreateUserDto, userData);

    // Create user logic here...
    const newUserData = {
      id: generateId(),
      ...createUserDto,
      createdAt: new Date(),
    };

    // Transform to response DTO
    return transformToUserResponseDto(newUserData);
  }
}
```

### Key Features

1. **Automatic Validation**: All DTOs include validation decorators
2. **Data Transformation**: Automatic type conversion (strings to numbers, etc.)
3. **Data Sanitization**: Automatic trimming and case conversion
4. **API Documentation**: Swagger decorators for automatic API docs
5. **Type Safety**: Full TypeScript support

### Reflect Metadata

The library automatically imports `reflect-metadata` to ensure decorator metadata is available. This is essential for:

- Class-validator validation decorators
- Class-transformer type conversion
- NestJS dependency injection
- Swagger API documentation generation
