// Core DTO classes that work for both frontend and backend
// Frontend uses these as plain TypeScript classes
// Backend uses them with decorators when the decorator libraries are available

// Conditional decorator imports - these will be tree-shaken out in frontend builds
let ApiProperty: any,
  IsString: any,
  IsEmail: any,
  IsOptional: any,
  IsNumber: any,
  Min: any,
  IsUUID: any,
  IsDateString: any,
  Transform: any,
  Type: any;

try {
  // These imports will only succeed in backend environment
  require('reflect-metadata'); // Ensure reflect-metadata is loaded for decorators
  const nestjsSwagger = require('@nestjs/swagger');
  const classValidator = require('class-validator');
  const classTransformer = require('class-transformer');

  ApiProperty = nestjsSwagger.ApiProperty;
  IsString = classValidator.IsString;
  IsEmail = classValidator.IsEmail;
  IsOptional = classValidator.IsOptional;
  IsNumber = classValidator.IsNumber;
  Min = classValidator.Min;
  IsUUID = classValidator.IsUUID;
  IsDateString = classValidator.IsDateString;
  Transform = classTransformer.Transform;
  Type = classTransformer.Type;
} catch {
  // In frontend environment, create no-op decorators
  const noop = () => () => {};
  ApiProperty =
    IsString =
    IsEmail =
    IsOptional =
    IsNumber =
    Min =
    IsUUID =
    IsDateString =
    Transform =
    Type =
      noop;
}

export class CreateUserDto {
  @ApiProperty({ description: 'User name', example: 'John Doe' })
  @IsString()
  @Transform(({ value }: any) => value?.trim())
  name: string;

  @ApiProperty({ description: 'User email', example: '<EMAIL>' })
  @IsEmail()
  @Transform(({ value }: any) => value?.toLowerCase().trim())
  email: string;

  @ApiProperty({ description: 'User age', example: 25, required: false })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  age?: number;
}

export class UpdateUserDto {
  @ApiProperty({
    description: 'User name',
    example: 'John Doe',
    required: false,
  })
  @IsOptional()
  @IsString()
  @Transform(({ value }: any) => value?.trim())
  name?: string;

  @ApiProperty({
    description: 'User email',
    example: '<EMAIL>',
    required: false,
  })
  @IsOptional()
  @IsEmail()
  @Transform(({ value }: any) => value?.toLowerCase().trim())
  email?: string;

  @ApiProperty({ description: 'User age', example: 25, required: false })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  age?: number;
}

export class UserResponseDto {
  @ApiProperty({
    description: 'User ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  id: string;

  @ApiProperty({ description: 'User name', example: 'John Doe' })
  @IsString()
  name: string;

  @ApiProperty({ description: 'User email', example: '<EMAIL>' })
  @IsEmail()
  email: string;

  @ApiProperty({ description: 'User age', example: 25, required: false })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  age?: number;

  @ApiProperty({
    description: 'Creation timestamp',
    example: '2023-01-01T00:00:00.000Z',
  })
  @IsDateString()
  @Type(() => Date)
  createdAt: Date;
}
