import React from 'react';
// Test that our dynamic stubs handle ANY faker import
import { faker } from '@faker-js/faker';

// Test that our dynamic stubs handle ANY class-validator import
import { 
  validate, 
  IsString, 
  IsEmail,
  // These might not exist in the real class-validator, but our stub should handle them
  IsCustomThing,
  ValidateNested,
  IsArray
} from 'class-validator';

// Test that our dynamic stubs handle ANY NestJS import
import { ApiProperty, ApiResponse, ApiTags } from '@nestjs/swagger';

export function DynamicStubTest() {
  // Test faker with various property chains that might not exist
  const testFakerChaining = () => {
    console.log('Testing faker chaining:');
    
    // Standard faker usage
    console.log('UUID:', faker.string.uuid());
    console.log('Name:', faker.person.firstName());
    console.log('Email:', faker.internet.email());
    
    // Non-existent faker chains - should still work
    console.log('Custom chain 1:', faker.custom.deeply.nested.property());
    console.log('Custom chain 2:', faker.newModule.someFunction());
    console.log('Custom chain 3:', faker.future.feature.that.doesnt.exist.yet());
  };

  // Test class-validator decorators
  const testValidatorDecorators = () => {
    console.log('Testing validator decorators:');
    
    // Standard decorators
    const stringDecorator = IsString();
    const emailDecorator = IsEmail();
    
    // Non-existent decorators - should still work
    const customDecorator = IsCustomThing();
    const nestedDecorator = ValidateNested();
    const arrayDecorator = IsArray();
    
    console.log('All decorators created successfully');
    
    // Test validation
    validate({}).then(errors => {
      console.log('Validation result:', errors);
    });
  };

  // Test NestJS decorators
  const testNestJSDecorators = () => {
    console.log('Testing NestJS decorators:');
    
    // Standard decorators
    const apiProp = ApiProperty();
    const apiResp = ApiResponse();
    
    // Non-existent decorators - should still work
    const apiTags = ApiTags();
    
    console.log('All NestJS decorators created successfully');
  };

  return (
    <div style={{ padding: '20px', border: '1px solid #green', margin: '10px' }}>
      <h3>Dynamic Stub Test Component</h3>
      <p>This component tests that our dynamic stubs can handle ANY import from backend libraries.</p>
      
      <div style={{ marginBottom: '20px' }}>
        <button onClick={testFakerChaining} style={{ marginRight: '10px', padding: '8px 16px' }}>
          Test Faker Chaining
        </button>
        <button onClick={testValidatorDecorators} style={{ marginRight: '10px', padding: '8px 16px' }}>
          Test Validator Decorators
        </button>
        <button onClick={testNestJSDecorators} style={{ padding: '8px 16px' }}>
          Test NestJS Decorators
        </button>
      </div>

      <div style={{ background: '#f5f5f5', padding: '10px', fontSize: '12px' }}>
        <h4>What this tests:</h4>
        <ul>
          <li>✅ <strong>Faker chaining:</strong> faker.any.property.chain.works()</li>
          <li>✅ <strong>Unknown decorators:</strong> @IsCustomThing(), @ValidateNested()</li>
          <li>✅ <strong>Future imports:</strong> New functions added to libraries</li>
          <li>✅ <strong>No build updates needed:</strong> Common lib can import anything</li>
        </ul>
        
        <p style={{ color: '#666', marginTop: '10px' }}>
          <strong>Key benefit:</strong> The common library can add new imports from faker, 
          class-validator, or NestJS without requiring any updates to the frontend build configuration!
        </p>
      </div>

      <div style={{ background: '#e8f5e8', padding: '10px', fontSize: '12px', marginTop: '10px' }}>
        <h4>Example: If common lib adds this tomorrow...</h4>
        <pre style={{ background: 'white', padding: '5px' }}>
{`// In common/src/lib/new-feature.ts
import { faker } from '@faker-js/faker';
import { IsCustomValidator } from 'class-validator';

export const newFunction = () => {
  return faker.newModule.brandNewFunction(); // ✅ Works automatically
};

export class NewDto {
  @IsCustomValidator() // ✅ Works automatically
  customField: string;
}`}
        </pre>
        <p style={{ color: '#006600', marginTop: '5px' }}>
          <strong>✅ Frontend build will continue to work without any changes!</strong>
        </p>
      </div>
    </div>
  );
}
