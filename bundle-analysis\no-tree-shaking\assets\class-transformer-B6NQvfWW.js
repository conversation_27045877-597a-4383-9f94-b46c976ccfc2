function dt(r){return r&&r.__esModule&&Object.prototype.hasOwnProperty.call(r,"default")?r.default:r}function R(r){if(Object.prototype.hasOwnProperty.call(r,"__esModule"))return r;var t=r.default;if(typeof t=="function"){var n=function e(){return this instanceof e?Reflect.construct(t,arguments,this.constructor):t.apply(this,arguments)};n.prototype=t.prototype}else n={};return Object.defineProperty(n,"__esModule",{value:!0}),Object.keys(r).forEach(function(e){var o=Object.getOwnPropertyDescriptor(r,e);Object.defineProperty(n,e,o.get?o:{enumerable:!0,get:function(){return r[e]}})}),n}var p;(function(r){r[r.PLAIN_TO_CLASS=0]="PLAIN_TO_CLASS",r[r.CLASS_TO_PLAIN=1]="CLASS_TO_PLAIN",r[r.CLASS_TO_CLASS=2]="CLASS_TO_CLASS"})(p||(p={}));var J=function(){function r(){this._typeMetadatas=new Map,this._transformMetadatas=new Map,this._exposeMetadatas=new Map,this._excludeMetadatas=new Map,this._ancestorsMap=new Map}return r.prototype.addTypeMetadata=function(t){this._typeMetadatas.has(t.target)||this._typeMetadatas.set(t.target,new Map),this._typeMetadatas.get(t.target).set(t.propertyName,t)},r.prototype.addTransformMetadata=function(t){this._transformMetadatas.has(t.target)||this._transformMetadatas.set(t.target,new Map),this._transformMetadatas.get(t.target).has(t.propertyName)||this._transformMetadatas.get(t.target).set(t.propertyName,[]),this._transformMetadatas.get(t.target).get(t.propertyName).push(t)},r.prototype.addExposeMetadata=function(t){this._exposeMetadatas.has(t.target)||this._exposeMetadatas.set(t.target,new Map),this._exposeMetadatas.get(t.target).set(t.propertyName,t)},r.prototype.addExcludeMetadata=function(t){this._excludeMetadatas.has(t.target)||this._excludeMetadatas.set(t.target,new Map),this._excludeMetadatas.get(t.target).set(t.propertyName,t)},r.prototype.findTransformMetadatas=function(t,n,e){return this.findMetadatas(this._transformMetadatas,t,n).filter(function(o){return!o.options||o.options.toClassOnly===!0&&o.options.toPlainOnly===!0?!0:o.options.toClassOnly===!0?e===p.CLASS_TO_CLASS||e===p.PLAIN_TO_CLASS:o.options.toPlainOnly===!0?e===p.CLASS_TO_PLAIN:!0})},r.prototype.findExcludeMetadata=function(t,n){return this.findMetadata(this._excludeMetadatas,t,n)},r.prototype.findExposeMetadata=function(t,n){return this.findMetadata(this._exposeMetadatas,t,n)},r.prototype.findExposeMetadataByCustomName=function(t,n){return this.getExposedMetadatas(t).find(function(e){return e.options&&e.options.name===n})},r.prototype.findTypeMetadata=function(t,n){return this.findMetadata(this._typeMetadatas,t,n)},r.prototype.getStrategy=function(t){var n=this._excludeMetadatas.get(t),e=n&&n.get(void 0),o=this._exposeMetadatas.get(t),u=o&&o.get(void 0);return e&&u||!e&&!u?"none":e?"excludeAll":"exposeAll"},r.prototype.getExposedMetadatas=function(t){return this.getMetadata(this._exposeMetadatas,t)},r.prototype.getExcludedMetadatas=function(t){return this.getMetadata(this._excludeMetadatas,t)},r.prototype.getExposedProperties=function(t,n){return this.getExposedMetadatas(t).filter(function(e){return!e.options||e.options.toClassOnly===!0&&e.options.toPlainOnly===!0?!0:e.options.toClassOnly===!0?n===p.CLASS_TO_CLASS||n===p.PLAIN_TO_CLASS:e.options.toPlainOnly===!0?n===p.CLASS_TO_PLAIN:!0}).map(function(e){return e.propertyName})},r.prototype.getExcludedProperties=function(t,n){return this.getExcludedMetadatas(t).filter(function(e){return!e.options||e.options.toClassOnly===!0&&e.options.toPlainOnly===!0?!0:e.options.toClassOnly===!0?n===p.CLASS_TO_CLASS||n===p.PLAIN_TO_CLASS:e.options.toPlainOnly===!0?n===p.CLASS_TO_PLAIN:!0}).map(function(e){return e.propertyName})},r.prototype.clear=function(){this._typeMetadatas.clear(),this._exposeMetadatas.clear(),this._excludeMetadatas.clear(),this._ancestorsMap.clear()},r.prototype.getMetadata=function(t,n){var e=t.get(n),o;e&&(o=Array.from(e.values()).filter(function(y){return y.propertyName!==void 0}));for(var u=[],s=0,a=this.getAncestors(n);s<a.length;s++){var f=a[s],l=t.get(f);if(l){var c=Array.from(l.values()).filter(function(y){return y.propertyName!==void 0});u.push.apply(u,c)}}return u.concat(o||[])},r.prototype.findMetadata=function(t,n,e){var o=t.get(n);if(o){var u=o.get(e);if(u)return u}for(var s=0,a=this.getAncestors(n);s<a.length;s++){var f=a[s],l=t.get(f);if(l){var c=l.get(e);if(c)return c}}},r.prototype.findMetadatas=function(t,n,e){var o=t.get(n),u;o&&(u=o.get(e));for(var s=[],a=0,f=this.getAncestors(n);a<f.length;a++){var l=f[a],c=t.get(l);c&&c.has(e)&&s.push.apply(s,c.get(e))}return s.slice().reverse().concat((u||[]).slice().reverse())},r.prototype.getAncestors=function(t){if(!t)return[];if(!this._ancestorsMap.has(t)){for(var n=[],e=Object.getPrototypeOf(t.prototype.constructor);typeof e.prototype<"u";e=Object.getPrototypeOf(e.prototype.constructor))n.push(e);this._ancestorsMap.set(t,n)}return this._ancestorsMap.get(t)},r}(),m=new J;function U(){if(typeof globalThis<"u"||typeof globalThis<"u")return globalThis;if(typeof window<"u")return window;if(typeof self<"u")return self}function $(r){return r!==null&&typeof r=="object"&&typeof r.then=="function"}var B=function(r,t,n){if(n||arguments.length===2)for(var e=0,o=t.length,u;e<o;e++)(u||!(e in t))&&(u||(u=Array.prototype.slice.call(t,0,e)),u[e]=t[e]);return r.concat(u||Array.prototype.slice.call(t))};function q(r){var t=new r;return!(t instanceof Set)&&!("push"in t)?[]:t}var _=function(){function r(t,n){this.transformationType=t,this.options=n,this.recursionStack=new Set}return r.prototype.transform=function(t,n,e,o,u,s){var a=this;if(s===void 0&&(s=0),Array.isArray(n)||n instanceof Set){var f=o&&this.transformationType===p.PLAIN_TO_CLASS?q(o):[];return n.forEach(function(h,C){var M=t?t[C]:void 0;if(!a.options.enableCircularCheck||!a.isCircular(h)){var g=void 0;if(typeof e!="function"&&e&&e.options&&e.options.discriminator&&e.options.discriminator.property&&e.options.discriminator.subTypes){if(a.transformationType===p.PLAIN_TO_CLASS){g=e.options.discriminator.subTypes.find(function(v){return v.name===h[e.options.discriminator.property]});var L={newObject:f,object:h,property:void 0},d=e.typeFunction(L);g===void 0?g=d:g=g.value,e.options.keepDiscriminatorProperty||delete h[e.options.discriminator.property]}a.transformationType===p.CLASS_TO_CLASS&&(g=h.constructor),a.transformationType===p.CLASS_TO_PLAIN&&(h[e.options.discriminator.property]=e.options.discriminator.subTypes.find(function(v){return v.value===h.constructor}).name)}else g=e;var O=a.transform(M,h,g,void 0,h instanceof Map,s+1);f instanceof Set?f.add(O):f.push(O)}else a.transformationType===p.CLASS_TO_CLASS&&(f instanceof Set?f.add(h):f.push(h))}),f}else{if(e===String&&!u)return n==null?n:String(n);if(e===Number&&!u)return n==null?n:Number(n);if(e===Boolean&&!u)return n==null?n:!!n;if((e===Date||n instanceof Date)&&!u)return n instanceof Date?new Date(n.valueOf()):n==null?n:new Date(n);if(U().Buffer&&(e===Buffer||n instanceof Buffer)&&!u)return n==null?n:Buffer.from(n);if($(n)&&!u)return new Promise(function(h,C){n.then(function(M){return h(a.transform(void 0,M,e,void 0,void 0,s+1))},C)});if(!u&&n!==null&&typeof n=="object"&&typeof n.then=="function")return n;if(typeof n=="object"&&n!==null){!e&&n.constructor!==Object&&(!Array.isArray(n)&&n.constructor===Array||(e=n.constructor)),!e&&t&&(e=t.constructor),this.options.enableCircularCheck&&this.recursionStack.add(n);var l=this.getKeys(e,n,u),c=t||{};!t&&(this.transformationType===p.PLAIN_TO_CLASS||this.transformationType===p.CLASS_TO_CLASS)&&(u?c=new Map:e?c=new e:c={});for(var y=function(h){if(h==="__proto__"||h==="constructor")return"continue";var C=h,M=h,g=h;if(!i.options.ignoreDecorators&&e){if(i.transformationType===p.PLAIN_TO_CLASS){var L=m.findExposeMetadataByCustomName(e,h);L&&(g=L.propertyName,M=L.propertyName)}else if(i.transformationType===p.CLASS_TO_PLAIN||i.transformationType===p.CLASS_TO_CLASS){var L=m.findExposeMetadata(e,h);L&&L.options&&L.options.name&&(M=L.options.name)}}var d=void 0;i.transformationType===p.PLAIN_TO_CLASS?d=n[C]:n instanceof Map?d=n.get(C):n[C]instanceof Function?d=n[C]():d=n[C];var O=void 0,v=d instanceof Map;if(e&&u)O=e;else if(e){var A=m.findTypeMetadata(e,g);if(A){var K={newObject:c,object:n,property:g},F=A.typeFunction?A.typeFunction(K):A.reflectedType;A.options&&A.options.discriminator&&A.options.discriminator.property&&A.options.discriminator.subTypes?n[C]instanceof Array?O=A:(i.transformationType===p.PLAIN_TO_CLASS&&(O=A.options.discriminator.subTypes.find(function(P){if(d&&d instanceof Object&&A.options.discriminator.property in d)return P.name===d[A.options.discriminator.property]}),O===void 0?O=F:O=O.value,A.options.keepDiscriminatorProperty||d&&d instanceof Object&&A.options.discriminator.property in d&&delete d[A.options.discriminator.property]),i.transformationType===p.CLASS_TO_CLASS&&(O=d.constructor),i.transformationType===p.CLASS_TO_PLAIN&&d&&(d[A.options.discriminator.property]=A.options.discriminator.subTypes.find(function(P){return P.value===d.constructor}).name)):O=F,v=v||A.reflectedType===Map}else if(i.options.targetMaps)i.options.targetMaps.filter(function(P){return P.target===e&&!!P.properties[g]}).forEach(function(P){return O=P.properties[g]});else if(i.options.enableImplicitConversion&&i.transformationType===p.PLAIN_TO_CLASS){var w=Reflect.getMetadata("design:type",e.prototype,g);w&&(O=w)}}var D=Array.isArray(n[C])?i.getReflectedType(e,g):void 0,k=t?t[C]:void 0;if(c.constructor.prototype){var z=Object.getOwnPropertyDescriptor(c.constructor.prototype,M);if((i.transformationType===p.PLAIN_TO_CLASS||i.transformationType===p.CLASS_TO_CLASS)&&(z&&!z.set||c[M]instanceof Function))return"continue"}if(!i.options.enableCircularCheck||!i.isCircular(d)){var E=i.transformationType===p.PLAIN_TO_CLASS?M:h,S=void 0;i.transformationType===p.CLASS_TO_PLAIN?(S=n[E],S=i.applyCustomTransformations(S,e,E,n,i.transformationType),S=n[E]===S?d:S,S=i.transform(k,S,O,D,v,s+1)):d===void 0&&i.options.exposeDefaultValues?S=c[M]:(S=i.transform(k,d,O,D,v,s+1),S=i.applyCustomTransformations(S,e,E,n,i.transformationType)),(S!==void 0||i.options.exposeUnsetFields)&&(c instanceof Map?c.set(M,S):c[M]=S)}else if(i.transformationType===p.CLASS_TO_CLASS){var S=d;S=i.applyCustomTransformations(S,e,h,n,i.transformationType),(S!==void 0||i.options.exposeUnsetFields)&&(c instanceof Map?c.set(M,S):c[M]=S)}},i=this,b=0,j=l;b<j.length;b++){var G=j[b];y(G)}return this.options.enableCircularCheck&&this.recursionStack.delete(n),c}else return n}},r.prototype.applyCustomTransformations=function(t,n,e,o,u){var s=this,a=m.findTransformMetadatas(n,e,this.transformationType);return this.options.version!==void 0&&(a=a.filter(function(f){return f.options?s.checkVersion(f.options.since,f.options.until):!0})),this.options.groups&&this.options.groups.length?a=a.filter(function(f){return f.options?s.checkGroups(f.options.groups):!0}):a=a.filter(function(f){return!f.options||!f.options.groups||!f.options.groups.length}),a.forEach(function(f){t=f.transformFn({value:t,key:e,obj:o,type:u,options:s.options})}),t},r.prototype.isCircular=function(t){return this.recursionStack.has(t)},r.prototype.getReflectedType=function(t,n){if(t){var e=m.findTypeMetadata(t,n);return e?e.reflectedType:void 0}},r.prototype.getKeys=function(t,n,e){var o=this,u=m.getStrategy(t);u==="none"&&(u=this.options.strategy||"exposeAll");var s=[];if((u==="exposeAll"||e)&&(n instanceof Map?s=Array.from(n.keys()):s=Object.keys(n)),e)return s;if(this.options.ignoreDecorators&&this.options.excludeExtraneousValues&&t){var a=m.getExposedProperties(t,this.transformationType),f=m.getExcludedProperties(t,this.transformationType);s=B(B([],a,!0),f,!0)}if(!this.options.ignoreDecorators&&t){var a=m.getExposedProperties(t,this.transformationType);this.transformationType===p.PLAIN_TO_CLASS&&(a=a.map(function(y){var i=m.findExposeMetadata(t,y);return i&&i.options&&i.options.name?i.options.name:y})),this.options.excludeExtraneousValues?s=a:s=s.concat(a);var l=m.getExcludedProperties(t,this.transformationType);l.length>0&&(s=s.filter(function(y){return!l.includes(y)})),this.options.version!==void 0&&(s=s.filter(function(y){var i=m.findExposeMetadata(t,y);return!i||!i.options?!0:o.checkVersion(i.options.since,i.options.until)})),this.options.groups&&this.options.groups.length?s=s.filter(function(y){var i=m.findExposeMetadata(t,y);return!i||!i.options?!0:o.checkGroups(i.options.groups)}):s=s.filter(function(y){var i=m.findExposeMetadata(t,y);return!i||!i.options||!i.options.groups||!i.options.groups.length})}return this.options.excludePrefixes&&this.options.excludePrefixes.length&&(s=s.filter(function(c){return o.options.excludePrefixes.every(function(y){return c.substr(0,y.length)!==y})})),s=s.filter(function(c,y,i){return i.indexOf(c)===y}),s},r.prototype.checkVersion=function(t,n){var e=!0;return e&&t&&(e=this.options.version>=t),e&&n&&(e=this.options.version<n),e},r.prototype.checkGroups=function(t){return t?this.options.groups.some(function(n){return t.includes(n)}):!0},r}(),N={enableCircularCheck:!1,enableImplicitConversion:!1,excludeExtraneousValues:!1,excludePrefixes:void 0,exposeDefaultValues:!1,exposeUnsetFields:!0,groups:void 0,ignoreDecorators:!1,strategy:void 0,targetMaps:void 0,version:void 0},T=function(){return T=Object.assign||function(r){for(var t,n=1,e=arguments.length;n<e;n++){t=arguments[n];for(var o in t)Object.prototype.hasOwnProperty.call(t,o)&&(r[o]=t[o])}return r},T.apply(this,arguments)},I=function(){function r(){}return r.prototype.instanceToPlain=function(t,n){var e=new _(p.CLASS_TO_PLAIN,T(T({},N),n));return e.transform(void 0,t,void 0,void 0,void 0,void 0)},r.prototype.classToPlainFromExist=function(t,n,e){var o=new _(p.CLASS_TO_PLAIN,T(T({},N),e));return o.transform(n,t,void 0,void 0,void 0,void 0)},r.prototype.plainToInstance=function(t,n,e){var o=new _(p.PLAIN_TO_CLASS,T(T({},N),e));return o.transform(void 0,n,t,void 0,void 0,void 0)},r.prototype.plainToClassFromExist=function(t,n,e){var o=new _(p.PLAIN_TO_CLASS,T(T({},N),e));return o.transform(t,n,void 0,void 0,void 0,void 0)},r.prototype.instanceToInstance=function(t,n){var e=new _(p.CLASS_TO_CLASS,T(T({},N),n));return e.transform(void 0,t,void 0,void 0,void 0,void 0)},r.prototype.classToClassFromExist=function(t,n,e){var o=new _(p.CLASS_TO_CLASS,T(T({},N),e));return o.transform(n,t,void 0,void 0,void 0,void 0)},r.prototype.serialize=function(t,n){return JSON.stringify(this.instanceToPlain(t,n))},r.prototype.deserialize=function(t,n,e){var o=JSON.parse(n);return this.plainToInstance(t,o,e)},r.prototype.deserializeArray=function(t,n,e){var o=JSON.parse(n);return this.plainToInstance(t,o,e)},r}();function H(r){return r===void 0&&(r={}),function(t,n){m.addExcludeMetadata({target:t instanceof Function?t:t.constructor,propertyName:n,options:r})}}function Q(r){return r===void 0&&(r={}),function(t,n){m.addExposeMetadata({target:t instanceof Function?t:t.constructor,propertyName:n,options:r})}}function W(r){return function(t,n,e){var o=new I,u=e.value;e.value=function(){for(var s=[],a=0;a<arguments.length;a++)s[a]=arguments[a];var f=u.apply(this,s),l=!!f&&(typeof f=="object"||typeof f=="function")&&typeof f.then=="function";return l?f.then(function(c){return o.instanceToInstance(c,r)}):o.instanceToInstance(f,r)}}}function X(r){return function(t,n,e){var o=new I,u=e.value;e.value=function(){for(var s=[],a=0;a<arguments.length;a++)s[a]=arguments[a];var f=u.apply(this,s),l=!!f&&(typeof f=="object"||typeof f=="function")&&typeof f.then=="function";return l?f.then(function(c){return o.instanceToPlain(c,r)}):o.instanceToPlain(f,r)}}}function Y(r,t){return function(n,e,o){var u=new I,s=o.value;o.value=function(){for(var a=[],f=0;f<arguments.length;f++)a[f]=arguments[f];var l=s.apply(this,a),c=!!l&&(typeof l=="object"||typeof l=="function")&&typeof l.then=="function";return c?l.then(function(y){return u.plainToInstance(r,y,t)}):u.plainToInstance(r,l,t)}}}function Z(r,t){return t===void 0&&(t={}),function(n,e){m.addTransformMetadata({target:n.constructor,propertyName:e,transformFn:r,options:t})}}function V(r,t){return t===void 0&&(t={}),function(n,e){var o=Reflect.getMetadata("design:type",n,e);m.addTypeMetadata({target:n.constructor,propertyName:e,reflectedType:o,typeFunction:r,options:t})}}var x=new I;function tt(r,t){return x.instanceToPlain(r,t)}function nt(r,t){return x.instanceToPlain(r,t)}function et(r,t,n){return x.classToPlainFromExist(r,t,n)}function rt(r,t,n){return x.plainToInstance(r,t,n)}function ot(r,t,n){return x.plainToInstance(r,t,n)}function it(r,t,n){return x.plainToClassFromExist(r,t,n)}function st(r,t){return x.instanceToInstance(r,t)}function ft(r,t,n){return x.classToClassFromExist(r,t,n)}function at(r,t){return x.serialize(r,t)}function ut(r,t,n){return x.deserialize(r,t,n)}function pt(r,t,n){return x.deserializeArray(r,t,n)}const ct=Object.freeze(Object.defineProperty({__proto__:null,ClassTransformer:I,Exclude:H,Expose:Q,Transform:Z,TransformInstanceToInstance:W,TransformInstanceToPlain:X,TransformPlainToInstance:Y,get TransformationType(){return p},Type:V,classToClassFromExist:ft,classToPlain:tt,classToPlainFromExist:et,deserialize:ut,deserializeArray:pt,instanceToInstance:st,instanceToPlain:nt,plainToClass:rt,plainToClassFromExist:it,plainToInstance:ot,serialize:at},Symbol.toStringTag,{value:"Module"})),lt=R(ct);export{R as a,dt as g,lt as r};
