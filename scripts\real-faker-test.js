#!/usr/bin/env node

/**
 * Real Faker Bundle Test Script
 * 
 * This script tests the impact of actually including faker vs stubbing it:
 * 1. Light component with faker stubbed (current config)
 * 2. Heavy component with faker stubbed (current config)
 * 3. Heavy component with real faker (no stubbing)
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

const FRONTEND_DIR = path.join(__dirname, '../apps/frontend');
const APP_TSX_PATH = path.join(FRONTEND_DIR, 'src/app/app.tsx');
const VITE_CONFIG_PATH = path.join(FRONTEND_DIR, 'vite.config.ts');
const DIST_DIR = path.join(FRONTEND_DIR, 'dist');
const RESULTS_DIR = path.join(__dirname, '../real-faker-analysis');

// Ensure results directory exists
if (!fs.existsSync(RESULTS_DIR)) {
  fs.mkdirSync(RESULTS_DIR, { recursive: true });
}

console.log('🧪 Real Faker Bundle Test: Stubbing vs Real Faker Impact');
console.log('======================================================\n');

// Read original files
const originalAppTsx = fs.readFileSync(APP_TSX_PATH, 'utf8');
const originalViteConfig = fs.readFileSync(VITE_CONFIG_PATH, 'utf8');

// Create backups
fs.writeFileSync(APP_TSX_PATH + '.backup', originalAppTsx);
fs.writeFileSync(VITE_CONFIG_PATH + '.backup', originalViteConfig);

function cleanDist() {
  if (fs.existsSync(DIST_DIR)) {
    fs.rmSync(DIST_DIR, { recursive: true, force: true });
  }
}

function getBundleStats(distPath) {
  const stats = {};
  
  if (!fs.existsSync(distPath)) {
    return stats;
  }

  const assetsPath = path.join(distPath, 'assets');
  const directories = [distPath];
  
  if (fs.existsSync(assetsPath)) {
    directories.push(assetsPath);
  }
  
  directories.forEach(dir => {
    const files = fs.readdirSync(dir);
    
    files.forEach(file => {
      const filePath = path.join(dir, file);
      const stat = fs.statSync(filePath);
      
      if (stat.isFile() && (file.endsWith('.js') || file.endsWith('.css'))) {
        stats[file] = {
          size: stat.size,
          sizeKB: Math.round(stat.size / 1024 * 100) / 100
        };
      }
    });
  });

  return stats;
}

function analyzeBundle(distPath, label) {
  console.log(`\n📊 Analyzing ${label}:`);
  console.log('─'.repeat(50));
  
  const stats = getBundleStats(distPath);
  let totalSize = 0;
  
  Object.entries(stats).forEach(([file, info]) => {
    console.log(`  ${file}: ${info.sizeKB} KB`);
    totalSize += info.size;
  });
  
  console.log(`  Total: ${Math.round(totalSize / 1024 * 100) / 100} KB`);
  
  return { stats, totalSize };
}

function buildWithConfig(appModifier, viteModifier, label) {
  console.log(`\n🔨 Building ${label}...`);
  
  // Modify app.tsx if needed
  if (appModifier) {
    const modifiedApp = appModifier(originalAppTsx);
    fs.writeFileSync(APP_TSX_PATH, modifiedApp);
  }
  
  // Modify vite config if needed
  if (viteModifier) {
    const modifiedVite = viteModifier(originalViteConfig);
    fs.writeFileSync(VITE_CONFIG_PATH, modifiedVite);
  }
  
  // Clean and build
  cleanDist();
  
  try {
    execSync('pnpm nx build frontend', { 
      cwd: path.join(__dirname, '..'), 
      stdio: 'pipe' 
    });
    
    // Copy dist to results for comparison
    const resultPath = path.join(RESULTS_DIR, label.toLowerCase().replace(/\s+/g, '-'));
    if (fs.existsSync(resultPath)) {
      fs.rmSync(resultPath, { recursive: true, force: true });
    }
    fs.cpSync(DIST_DIR, resultPath, { recursive: true });
    
    return analyzeBundle(DIST_DIR, label);
    
  } catch (error) {
    console.error(`❌ Build failed for ${label}:`, error.message);
    return null;
  }
}

// Test scenarios
const tests = [
  {
    label: 'Light Component (Faker Stubbed)',
    appModifier: null, // Use current TreeShakingTest component
    viteModifier: null // Use current config with faker stubbing
  },
  {
    label: 'Heavy Component (Faker Stubbed)',
    appModifier: (content) => {
      // Replace TreeShakingTest import with HeavyTestComponent
      return content
        .replace("import { TreeShakingTest } from '../components/TreeShakingTest';", 
                 "import { HeavyTestComponent } from '../components/HeavyTestComponent';")
        .replace('<TreeShakingTest />', '<HeavyTestComponent />');
    },
    viteModifier: null // Keep faker stubbing
  },
  {
    label: 'Heavy Component (Real Faker)',
    appModifier: (content) => {
      return content
        .replace("import { TreeShakingTest } from '../components/TreeShakingTest';", 
                 "import { HeavyTestComponent } from '../components/HeavyTestComponent';")
        .replace('<TreeShakingTest />', '<HeavyTestComponent />');
    },
    viteModifier: (config) => {
      // Remove faker stubbing from both plugin and alias
      let modifiedConfig = config
        // Remove faker from the plugin
        .replace(/if \(id === '@faker-js\/faker'\) \{[^}]*\}[^}]*\}/s, '')
        // Remove faker from alias
        .replace(/\{\s*find:\s*\/\^@faker-js\\\/faker\$\/,\s*replacement:\s*'[^']*',?\s*\},?/g, '')
        // Remove faker from optimizeDeps exclude
        .replace(/'@faker-js\/faker',?\s*/g, '');
      
      return modifiedConfig;
    }
  }
];

// Run all tests
const results = [];
for (const test of tests) {
  const result = buildWithConfig(test.appModifier, test.viteModifier, test.label);
  if (result) {
    results.push({ label: test.label, data: result });
  }
  
  // Restore original files between tests
  fs.writeFileSync(APP_TSX_PATH, originalAppTsx);
  fs.writeFileSync(VITE_CONFIG_PATH, originalViteConfig);
}

// Generate comparison report
console.log('\n📈 Real Faker Bundle Comparison Report');
console.log('='.repeat(60));

if (results.length > 0) {
  const baseline = results[0];
  
  results.forEach((result, index) => {
    if (index === 0) {
      console.log(`\n${result.label} (baseline):`);
      console.log(`  Total size: ${Math.round(result.data.totalSize / 1024 * 100) / 100} KB`);
    } else {
      const sizeDiff = result.data.totalSize - baseline.data.totalSize;
      const percentDiff = Math.round((sizeDiff / baseline.data.totalSize) * 100 * 100) / 100;
      
      console.log(`\n${result.label}:`);
      console.log(`  Total size: ${Math.round(result.data.totalSize / 1024 * 100) / 100} KB`);
      console.log(`  Difference: ${sizeDiff > 0 ? '+' : ''}${Math.round(sizeDiff / 1024 * 100) / 100} KB (${percentDiff > 0 ? '+' : ''}${percentDiff}%)`);
    }
  });
}

// Restore original files
fs.writeFileSync(APP_TSX_PATH, originalAppTsx);
fs.writeFileSync(VITE_CONFIG_PATH, originalViteConfig);
fs.unlinkSync(APP_TSX_PATH + '.backup');
fs.unlinkSync(VITE_CONFIG_PATH + '.backup');

console.log(`\n📁 Bundle files saved to: ${RESULTS_DIR}`);
console.log('\n✅ Real faker test completed!');
console.log('\n💡 Key findings:');
console.log('   - Light component: Only DTOs, no faker needed');
console.log('   - Heavy component (stubbed): Uses fake faker, small size');
console.log('   - Heavy component (real): Uses real faker, should be MUCH larger');
console.log('   - This demonstrates the power of tree-shaking and import stubbing');
