import{a as q,r as F,j as r,b,L as v,R as L,c as S,d as M,B as $}from"./vendor-BiO7oy6w.js";import{a as D,r as I}from"./class-transformer-CJFEAE5x.js";(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))m(o);new MutationObserver(o=>{for(const i of o)if(i.type==="childList")for(const c of i.addedNodes)c.tagName==="LINK"&&c.rel==="modulepreload"&&m(c)}).observe(document,{childList:!0,subtree:!0});function l(o){const i={};return o.integrity&&(i.integrity=o.integrity),o.referrerPolicy&&(i.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?i.credentials="include":o.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function m(o){if(o.ep)return;o.ep=!0;const i=l(o);fetch(o.href,i)}})();const z="http://localhost:3000/api",j=q.create({baseURL:z,headers:{"Content-Type":"application/json"}});class T{static async getUsers(){return(await j.get("/users")).data}static async getUser(t){return(await j.get(`/users/${t}`)).data}static async createUser(t){return(await j.post("/users",t)).data}static async updateUser(t,l){return(await j.put(`/users/${t}`,l)).data}}var U={},f={};const B=()=>()=>{},J={},H=Object.freeze(Object.defineProperty({__proto__:null,ApiProperty:B,default:J},Symbol.toStringTag,{value:"Module"})),V=D(H),X=()=>Promise.resolve([]),G={},K=Object.freeze(Object.defineProperty({__proto__:null,default:G,validate:X},Symbol.toStringTag,{value:"Module"})),E=D(K);var A;function O(){if(A)return f;A=1,Object.defineProperty(f,"__esModule",{value:!0}),f.UserResponseDto=f.UpdateUserDto=f.CreateUserDto=void 0;const e=F;let t,l,m,o,i,c,n,a,s,p;try{const d=V,y=E,w=I;t=d.ApiProperty,l=y.IsString,m=y.IsEmail,o=y.IsOptional,i=y.IsNumber,c=y.Min,n=y.IsUUID,a=y.IsDateString,s=w.Transform,p=w.Type}catch{t=l=m=o=i=c=n=a=s=p=()=>()=>{}}class _{}f.CreateUserDto=_,e.__decorate([t({description:"User name",example:"John Doe"}),l(),s(({value:d})=>d==null?void 0:d.trim()),e.__metadata("design:type",String)],_.prototype,"name",void 0),e.__decorate([t({description:"User email",example:"<EMAIL>"}),m(),s(({value:d})=>d==null?void 0:d.toLowerCase().trim()),e.__metadata("design:type",String)],_.prototype,"email",void 0),e.__decorate([t({description:"User age",example:25,required:!1}),o(),i(),c(0),p(()=>Number),e.__metadata("design:type",Number)],_.prototype,"age",void 0);class k{}f.UpdateUserDto=k,e.__decorate([t({description:"User name",example:"John Doe",required:!1}),o(),l(),s(({value:d})=>d==null?void 0:d.trim()),e.__metadata("design:type",String)],k.prototype,"name",void 0),e.__decorate([t({description:"User email",example:"<EMAIL>",required:!1}),o(),m(),s(({value:d})=>d==null?void 0:d.toLowerCase().trim()),e.__metadata("design:type",String)],k.prototype,"email",void 0),e.__decorate([t({description:"User age",example:25,required:!1}),o(),i(),c(0),p(()=>Number),e.__metadata("design:type",Number)],k.prototype,"age",void 0);class h{}return f.UserResponseDto=h,e.__decorate([t({description:"User ID",example:"123e4567-e89b-12d3-a456-************"}),n(),e.__metadata("design:type",String)],h.prototype,"id",void 0),e.__decorate([t({description:"User name",example:"John Doe"}),l(),e.__metadata("design:type",String)],h.prototype,"name",void 0),e.__decorate([t({description:"User email",example:"<EMAIL>"}),m(),e.__metadata("design:type",String)],h.prototype,"email",void 0),e.__decorate([t({description:"User age",example:25,required:!1}),o(),i(),p(()=>Number),e.__metadata("design:type",Number)],h.prototype,"age",void 0),e.__decorate([t({description:"Creation timestamp",example:"2023-01-01T00:00:00.000Z"}),a(),p(()=>Date),e.__metadata("design:type",Date)],h.prototype,"createdAt",void 0),f}var u={},N;function C(){if(N)return u;N=1,Object.defineProperty(u,"__esModule",{value:!0}),u.transformToUserResponseDto=m,u.transformToUserResponseDtoArray=o,u.serializeUserResponse=i,u.transformAndValidate=c,u.transformToCreateUserDto=n,u.transformToUpdateUserDto=a;const e=O(),t=I,l=E;function m(s){const p=new e.UserResponseDto;return Object.assign(p,s),s.createdAt&&typeof s.createdAt=="string"&&(p.createdAt=new Date(s.createdAt)),p}function o(s){return s.map(p=>m(p))}function i(s){return{id:s.id,name:s.name,email:s.email,age:s.age,createdAt:s.createdAt}}async function c(s,p){const _=(0,t.plainToInstance)(s,p),k=await(0,l.validate)(_);if(k.length>0){const h=k.map(d=>Object.values(d.constraints||{}).join(", ")).join("; ");throw new Error(`Validation failed: ${h}`)}return _}async function n(s){return c(e.CreateUserDto,s)}async function a(s){return c(e.UpdateUserDto,s)}return u}var g={};const W={},Z={},Q=Object.freeze(Object.defineProperty({__proto__:null,default:Z,faker:W},Symbol.toStringTag,{value:"Module"})),Y=D(Q);var R;function ee(){if(R)return g;R=1,Object.defineProperty(g,"__esModule",{value:!0}),g.generateComplexFakeData=t,g.generateFakeEcommerceData=l,g.generateFakeDatabaseRecords=m,g.generateFakeAnimalFoodData=o,g.generateComprehensiveFakeData=i;const e=Y;function t(c=10){const n=[];for(let a=0;a<c;a++)n.push({id:e.faker.string.uuid(),firstName:e.faker.person.firstName(),lastName:e.faker.person.lastName(),fullName:e.faker.person.fullName(),email:e.faker.internet.email(),username:e.faker.internet.username(),avatar:e.faker.image.avatar(),address:{street:e.faker.location.streetAddress(),city:e.faker.location.city(),state:e.faker.location.state(),zipCode:e.faker.location.zipCode(),country:e.faker.location.country(),coordinates:{lat:e.faker.location.latitude(),lng:e.faker.location.longitude()}},company:{name:e.faker.company.name(),department:e.faker.commerce.department(),jobTitle:e.faker.person.jobTitle(),catchPhrase:e.faker.company.catchPhrase(),bs:e.faker.company.buzzPhrase()},finance:{accountNumber:e.faker.finance.accountNumber(),routingNumber:e.faker.finance.routingNumber(),creditCardNumber:e.faker.finance.creditCardNumber(),iban:e.faker.finance.iban(),bitcoinAddress:e.faker.finance.bitcoinAddress()}});return{users:n,metadata:{generated:new Date,count:n.length,generator:"faker-heavy-utils"}}}function l(c=20){const n=[];for(let a=0;a<c;a++)n.push({id:e.faker.string.uuid(),name:e.faker.commerce.productName(),description:e.faker.commerce.productDescription(),price:e.faker.commerce.price(),department:e.faker.commerce.department(),material:e.faker.commerce.productMaterial(),color:e.faker.color.human(),vehicle:{manufacturer:e.faker.vehicle.manufacturer(),model:e.faker.vehicle.model(),type:e.faker.vehicle.type(),fuel:e.faker.vehicle.fuel(),vin:e.faker.vehicle.vin()},dates:{created:e.faker.date.past(),updated:e.faker.date.recent(),expires:e.faker.date.future()},metadata:{tags:e.faker.helpers.arrayElements(["popular","sale","new","featured","limited"],3),rating:e.faker.number.float({min:1,max:5,fractionDigits:1}),reviews:e.faker.number.int({min:0,max:1e3}),inStock:e.faker.datatype.boolean()}});return{products:n,summary:{totalProducts:n.length,averagePrice:n.reduce((a,s)=>a+parseFloat(s.price),0)/n.length,departments:[...new Set(n.map(a=>a.department))]}}}function m(c=30){const n=[];for(let a=0;a<c;a++)n.push({id:e.faker.string.uuid(),slug:e.faker.helpers.slugify(e.faker.lorem.words(3)),content:{title:e.faker.lorem.sentence(),paragraph:e.faker.lorem.paragraphs(3),words:e.faker.lorem.words(10),lines:e.faker.lorem.lines(5)},music:{genre:e.faker.music.genre(),songName:e.faker.music.songName()},book:{title:e.faker.book.title(),author:e.faker.book.author(),genre:e.faker.book.genre(),publisher:e.faker.book.publisher(),series:e.faker.book.series()},science:{chemicalElement:e.faker.science.chemicalElement(),unit:e.faker.science.unit()},system:{fileName:e.faker.system.fileName(),mimeType:e.faker.system.mimeType(),fileType:e.faker.system.fileType(),directoryPath:e.faker.system.directoryPath()},airline:{aircraftType:e.faker.airline.aircraftType(),airplane:e.faker.airline.airplane(),airport:e.faker.airline.airport(),seat:e.faker.airline.seat()}});return{records:n,analytics:{totalRecords:n.length,uniqueGenres:[...new Set(n.map(a=>a.music.genre))],averageContentLength:n.reduce((a,s)=>a+s.content.paragraph.length,0)/n.length}}}function o(c=15){const n=[];for(let a=0;a<c;a++)n.push({id:e.faker.string.uuid(),animal:{type:e.faker.animal.type(),dog:e.faker.animal.dog(),cat:e.faker.animal.cat(),bird:e.faker.animal.bird(),fish:e.faker.animal.fish(),horse:e.faker.animal.horse(),bear:e.faker.animal.bear(),cetacean:e.faker.animal.cetacean(),cow:e.faker.animal.cow(),crocodilia:e.faker.animal.crocodilia(),insect:e.faker.animal.insect(),lion:e.faker.animal.lion(),rabbit:e.faker.animal.rabbit(),rodent:e.faker.animal.rodent(),snake:e.faker.animal.snake()},food:{adjective:e.faker.food.adjective(),description:e.faker.food.description(),dish:e.faker.food.dish(),ethnicCategory:e.faker.food.ethnicCategory(),fruit:e.faker.food.fruit(),ingredient:e.faker.food.ingredient(),meat:e.faker.food.meat(),spice:e.faker.food.spice(),vegetable:e.faker.food.vegetable()},hacker:{abbreviation:e.faker.hacker.abbreviation(),adjective:e.faker.hacker.adjective(),ingverb:e.faker.hacker.ingverb(),noun:e.faker.hacker.noun(),phrase:e.faker.hacker.phrase(),verb:e.faker.hacker.verb()}});return{data:n,summary:{totalEntries:n.length,uniqueAnimalTypes:[...new Set(n.map(a=>a.animal.type))],uniqueFoodCategories:[...new Set(n.map(a=>a.food.ethnicCategory))]}}}function i(c=25){const n=[];for(let a=0;a<c;a++)n.push({id:e.faker.string.uuid(),git:{branch:e.faker.git.branch(),commitEntry:e.faker.git.commitEntry(),commitMessage:e.faker.git.commitMessage(),commitSha:e.faker.git.commitSha()},phone:{number:e.faker.phone.number(),imei:e.faker.phone.imei()},word:{adjective:e.faker.word.adjective(),adverb:e.faker.word.adverb(),conjunction:e.faker.word.conjunction(),interjection:e.faker.word.interjection(),noun:e.faker.word.noun(),preposition:e.faker.word.preposition(),verb:e.faker.word.verb(),words:e.faker.word.words(5)},database:{column:e.faker.database.column(),type:e.faker.database.type(),collation:e.faker.database.collation(),engine:e.faker.database.engine(),mongodbObjectId:e.faker.database.mongodbObjectId()},datatype:{boolean:e.faker.datatype.boolean(),float:e.faker.number.float(),hexadecimal:e.faker.string.hexadecimal(),number:e.faker.number.int(),uuid:e.faker.string.uuid(),json:JSON.stringify({key:e.faker.lorem.word(),value:e.faker.number.int(),active:e.faker.datatype.boolean()})}});return{data:n,metadata:{totalRecords:n.length,generatedAt:new Date,fakerModulesUsed:["git","phone","word","database","datatype","string","helpers","number"]}}}return g}var P;function re(){return P||(P=1,function(e){Object.defineProperty(e,"__esModule",{value:!0});const t=F;t.__exportStar(O(),e),t.__exportStar(C(),e),t.__exportStar(C(),e),t.__exportStar(ee(),e)}(U)),U}var x=re();function ae(){const e=x.generateComplexFakeData(1),t=x.generateFakeEcommerceData(1),l=x.generateFakeDatabaseRecords(1),m=x.generateFakeAnimalFoodData(1),o=x.generateComprehensiveFakeData(1),i={id:"123e4567-e89b-12d3-a456-************",name:"Test User",email:"<EMAIL>",age:25,createdAt:new Date};return r.jsxs("div",{style:{padding:"20px",border:"1px solid #red",margin:"10px"},children:[r.jsx("h3",{children:"Heavy Test Component (Uses ALL imports)"}),r.jsx("p",{children:"This component actually USES all the heavy utilities from @my-nx/common."}),r.jsx("p",{children:"This should result in a much larger bundle size because faker is included."}),r.jsxs("div",{children:[r.jsx("h4",{children:"Used DTOs:"}),r.jsx("pre",{style:{fontSize:"10px"},children:JSON.stringify(i,null,2)})]}),r.jsxs("div",{children:[r.jsx("h4",{children:"Heavy Data Generated (all functions used):"}),r.jsxs("div",{style:{maxHeight:"200px",overflow:"auto",fontSize:"10px"},children:[r.jsxs("p",{children:["Complex: ",e.users.length," users"]}),r.jsxs("p",{children:["Ecommerce: ",t.products.length," products"]}),r.jsxs("p",{children:["Database: ",l.records.length," records"]}),r.jsxs("p",{children:["Animal/Food: ",m.data.length," entries"]}),r.jsxs("p",{children:["Comprehensive: ",o.data.length," entries"]})]}),r.jsx("p",{style:{color:"red",fontSize:"12px"},children:r.jsx("strong",{children:"All heavy functions are called, so faker WILL be in the bundle!"})})]})]})}function te(){const[e,t]=b.useState([]),[l,m]=b.useState(!1),[o,i]=b.useState(null),c=async()=>{try{m(!0),i(null);const a=await T.getUsers();t(a)}catch(a){i("Failed to load users. Make sure the backend is running on port 3000."),console.error("Error loading users:",a)}finally{m(!1)}},n=async()=>{try{const a={name:"New User",email:`user${Date.now()}@example.com`,age:28};await T.createUser(a),await c()}catch(a){i("Failed to create user"),console.error("Error creating user:",a)}};return b.useEffect(()=>{c()},[]),r.jsxs("div",{style:{padding:"20px"},children:[r.jsx("h1",{children:"NX Monorepo Demo - Frontend"}),r.jsx("p",{children:"This frontend uses shared DTOs from the common package and communicates with the NestJS backend via axios."}),r.jsx("div",{role:"navigation",style:{marginBottom:"20px"},children:r.jsxs("ul",{style:{display:"flex",listStyle:"none",gap:"20px",padding:0},children:[r.jsx("li",{children:r.jsx(v,{to:"/",children:"Users"})}),r.jsx("li",{children:r.jsx(v,{to:"/about",children:"About"})})]})}),r.jsxs(L,{children:[r.jsx(S,{path:"/",element:r.jsxs("div",{children:[r.jsx("h2",{children:"Users Management"}),r.jsxs("div",{style:{marginBottom:"20px"},children:[r.jsx("button",{onClick:c,disabled:l,children:l?"Loading...":"Refresh Users"}),r.jsx("button",{onClick:n,style:{marginLeft:"10px"},children:"Add Sample User"})]}),o&&r.jsx("div",{style:{color:"red",marginBottom:"20px"},children:o}),r.jsxs("div",{children:[r.jsx("h3",{children:"Users List:"}),e.length===0?r.jsxs("p",{children:["No users found."," ",!o&&"Try adding a sample user or check if the backend is running."]}):r.jsx("ul",{children:e.map(a=>r.jsxs("li",{style:{marginBottom:"10px",padding:"10px",border:"1px solid #ccc"},children:[r.jsx("strong",{children:a.name})," (",a.email,")",a.age&&r.jsxs("span",{children:[" - Age: ",a.age]}),r.jsx("br",{}),r.jsxs("small",{children:["Created:"," ",new Date(a.createdAt).toLocaleDateString()]})]},a.id))})]})]})}),r.jsx(S,{path:"/about",element:r.jsxs("div",{children:[r.jsx("h2",{children:"About This Demo"}),r.jsx("p",{children:"This is a demonstration of an NX monorepo with:"}),r.jsxs("ul",{children:[r.jsx("li",{children:"✅ NX v20 with pnpm workspaces"}),r.jsx("li",{children:"✅ Shared common package with DTOs"}),r.jsx("li",{children:"✅ NestJS backend with TypeScript and webpack"}),r.jsx("li",{children:"✅ Vite React frontend"}),r.jsx("li",{children:"✅ Axios for API communication"}),r.jsx("li",{children:"✅ NestJS Swagger for API documentation"}),r.jsx("li",{children:"✅ Class-validator for request validation"}),r.jsx("li",{children:"✅ Reflect-metadata for decorators"}),r.jsx("li",{children:"✅ Tree-shaking working with faker"})]}),r.jsx(ae,{}),r.jsxs("p",{children:[r.jsx("strong",{children:"Backend:"})," ",r.jsx("a",{href:"http://localhost:3000/api/docs",target:"_blank",rel:"noopener noreferrer",children:"Swagger API Documentation"})]}),r.jsx(v,{to:"/",children:"← Back to Users"})]})})]})]})}const ne=M.createRoot(document.getElementById("root"));ne.render(r.jsx(b.StrictMode,{children:r.jsx($,{children:r.jsx(te,{})})}));
