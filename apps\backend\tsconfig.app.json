{"compilerOptions": {"outDir": "dist", "types": ["node"], "rootDir": "src", "tsBuildInfoFile": "dist/tsconfig.app.tsbuildinfo", "experimentalDecorators": true, "emitDecoratorMetadata": true, "target": "es2021", "module": "commonjs", "moduleResolution": "node", "composite": true, "declarationMap": true, "importHelpers": true, "isolatedModules": true, "lib": ["es2022"], "noEmitOnError": true, "noFallthroughCasesInSwitch": true, "noImplicitOverride": true, "noImplicitReturns": true, "noUnusedLocals": true, "skipLibCheck": true, "strict": true}, "include": ["src/**/*.ts"], "exclude": ["out-tsc", "dist", "jest.config.ts", "src/**/*.spec.ts", "src/**/*.test.ts", "eslint.config.js", "eslint.config.cjs", "eslint.config.mjs"], "references": [{"path": "../../packages/common/tsconfig.lib.json"}]}