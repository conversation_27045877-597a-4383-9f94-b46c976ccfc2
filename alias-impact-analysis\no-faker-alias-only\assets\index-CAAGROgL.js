import{a as q,r as P,b as _,j as r,L as v,R as M,c as w,d as L,B as $}from"./vendor-BiO7oy6w.js";import{a as F,r as I}from"./class-transformer-CJFEAE5x.js";import{r as z}from"./faker-B_gZNMpv.js";(function(){const n=document.createElement("link").relList;if(n&&n.supports&&n.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))m(o);new MutationObserver(o=>{for(const c of o)if(c.type==="childList")for(const i of c.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&m(i)}).observe(document,{childList:!0,subtree:!0});function d(o){const c={};return o.integrity&&(c.integrity=o.integrity),o.referrerPolicy&&(c.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?c.credentials="include":o.crossOrigin==="anonymous"?c.credentials="omit":c.credentials="same-origin",c}function m(o){if(o.ep)return;o.ep=!0;const c=d(o);fetch(o.href,c)}})();const B="http://localhost:3000/api",b=q.create({baseURL:B,headers:{"Content-Type":"application/json"}});class S{static async getUsers(){return(await b.get("/users")).data}static async getUser(n){return(await b.get(`/users/${n}`)).data}static async createUser(n){return(await b.post("/users",n)).data}static async updateUser(n,d){return(await b.put(`/users/${n}`,d)).data}}var U={},f={};const J=()=>()=>{},H={},V=Object.freeze(Object.defineProperty({__proto__:null,ApiProperty:J,default:H},Symbol.toStringTag,{value:"Module"})),G=F(V),X=()=>Promise.resolve([]),K={},Z=Object.freeze(Object.defineProperty({__proto__:null,default:K,validate:X},Symbol.toStringTag,{value:"Module"})),O=F(Z);var T;function E(){if(T)return f;T=1,Object.defineProperty(f,"__esModule",{value:!0}),f.UserResponseDto=f.UpdateUserDto=f.CreateUserDto=void 0;const e=P;let n,d,m,o,c,i,t,a,s,p;try{const l=G,y=O,D=I;n=l.ApiProperty,d=y.IsString,m=y.IsEmail,o=y.IsOptional,c=y.IsNumber,i=y.Min,t=y.IsUUID,a=y.IsDateString,s=D.Transform,p=D.Type}catch{n=d=m=o=c=i=t=a=s=p=()=>()=>{}}class k{}f.CreateUserDto=k,e.__decorate([n({description:"User name",example:"John Doe"}),d(),s(({value:l})=>l==null?void 0:l.trim()),e.__metadata("design:type",String)],k.prototype,"name",void 0),e.__decorate([n({description:"User email",example:"<EMAIL>"}),m(),s(({value:l})=>l==null?void 0:l.toLowerCase().trim()),e.__metadata("design:type",String)],k.prototype,"email",void 0),e.__decorate([n({description:"User age",example:25,required:!1}),o(),c(),i(0),p(()=>Number),e.__metadata("design:type",Number)],k.prototype,"age",void 0);class x{}f.UpdateUserDto=x,e.__decorate([n({description:"User name",example:"John Doe",required:!1}),o(),d(),s(({value:l})=>l==null?void 0:l.trim()),e.__metadata("design:type",String)],x.prototype,"name",void 0),e.__decorate([n({description:"User email",example:"<EMAIL>",required:!1}),o(),m(),s(({value:l})=>l==null?void 0:l.toLowerCase().trim()),e.__metadata("design:type",String)],x.prototype,"email",void 0),e.__decorate([n({description:"User age",example:25,required:!1}),o(),c(),i(0),p(()=>Number),e.__metadata("design:type",Number)],x.prototype,"age",void 0);class h{}return f.UserResponseDto=h,e.__decorate([n({description:"User ID",example:"123e4567-e89b-12d3-a456-************"}),t(),e.__metadata("design:type",String)],h.prototype,"id",void 0),e.__decorate([n({description:"User name",example:"John Doe"}),d(),e.__metadata("design:type",String)],h.prototype,"name",void 0),e.__decorate([n({description:"User email",example:"<EMAIL>"}),m(),e.__metadata("design:type",String)],h.prototype,"email",void 0),e.__decorate([n({description:"User age",example:25,required:!1}),o(),c(),p(()=>Number),e.__metadata("design:type",Number)],h.prototype,"age",void 0),e.__decorate([n({description:"Creation timestamp",example:"2023-01-01T00:00:00.000Z"}),a(),p(()=>Date),e.__metadata("design:type",Date)],h.prototype,"createdAt",void 0),f}var u={},N;function C(){if(N)return u;N=1,Object.defineProperty(u,"__esModule",{value:!0}),u.transformToUserResponseDto=m,u.transformToUserResponseDtoArray=o,u.serializeUserResponse=c,u.transformAndValidate=i,u.transformToCreateUserDto=t,u.transformToUpdateUserDto=a;const e=E(),n=I,d=O;function m(s){const p=new e.UserResponseDto;return Object.assign(p,s),s.createdAt&&typeof s.createdAt=="string"&&(p.createdAt=new Date(s.createdAt)),p}function o(s){return s.map(p=>m(p))}function c(s){return{id:s.id,name:s.name,email:s.email,age:s.age,createdAt:s.createdAt}}async function i(s,p){const k=(0,n.plainToInstance)(s,p),x=await(0,d.validate)(k);if(x.length>0){const h=x.map(l=>Object.values(l.constraints||{}).join(", ")).join("; ");throw new Error(`Validation failed: ${h}`)}return k}async function t(s){return i(e.CreateUserDto,s)}async function a(s){return i(e.UpdateUserDto,s)}return u}var g={},A;function Q(){if(A)return g;A=1,Object.defineProperty(g,"__esModule",{value:!0}),g.generateComplexFakeData=n,g.generateFakeEcommerceData=d,g.generateFakeDatabaseRecords=m,g.generateFakeAnimalFoodData=o,g.generateComprehensiveFakeData=c;const e=z();function n(i=10){const t=[];for(let a=0;a<i;a++)t.push({id:e.faker.string.uuid(),firstName:e.faker.person.firstName(),lastName:e.faker.person.lastName(),fullName:e.faker.person.fullName(),email:e.faker.internet.email(),username:e.faker.internet.username(),avatar:e.faker.image.avatar(),address:{street:e.faker.location.streetAddress(),city:e.faker.location.city(),state:e.faker.location.state(),zipCode:e.faker.location.zipCode(),country:e.faker.location.country(),coordinates:{lat:e.faker.location.latitude(),lng:e.faker.location.longitude()}},company:{name:e.faker.company.name(),department:e.faker.commerce.department(),jobTitle:e.faker.person.jobTitle(),catchPhrase:e.faker.company.catchPhrase(),bs:e.faker.company.buzzPhrase()},finance:{accountNumber:e.faker.finance.accountNumber(),routingNumber:e.faker.finance.routingNumber(),creditCardNumber:e.faker.finance.creditCardNumber(),iban:e.faker.finance.iban(),bitcoinAddress:e.faker.finance.bitcoinAddress()}});return{users:t,metadata:{generated:new Date,count:t.length,generator:"faker-heavy-utils"}}}function d(i=20){const t=[];for(let a=0;a<i;a++)t.push({id:e.faker.string.uuid(),name:e.faker.commerce.productName(),description:e.faker.commerce.productDescription(),price:e.faker.commerce.price(),department:e.faker.commerce.department(),material:e.faker.commerce.productMaterial(),color:e.faker.color.human(),vehicle:{manufacturer:e.faker.vehicle.manufacturer(),model:e.faker.vehicle.model(),type:e.faker.vehicle.type(),fuel:e.faker.vehicle.fuel(),vin:e.faker.vehicle.vin()},dates:{created:e.faker.date.past(),updated:e.faker.date.recent(),expires:e.faker.date.future()},metadata:{tags:e.faker.helpers.arrayElements(["popular","sale","new","featured","limited"],3),rating:e.faker.number.float({min:1,max:5,fractionDigits:1}),reviews:e.faker.number.int({min:0,max:1e3}),inStock:e.faker.datatype.boolean()}});return{products:t,summary:{totalProducts:t.length,averagePrice:t.reduce((a,s)=>a+parseFloat(s.price),0)/t.length,departments:[...new Set(t.map(a=>a.department))]}}}function m(i=30){const t=[];for(let a=0;a<i;a++)t.push({id:e.faker.string.uuid(),slug:e.faker.helpers.slugify(e.faker.lorem.words(3)),content:{title:e.faker.lorem.sentence(),paragraph:e.faker.lorem.paragraphs(3),words:e.faker.lorem.words(10),lines:e.faker.lorem.lines(5)},music:{genre:e.faker.music.genre(),songName:e.faker.music.songName()},book:{title:e.faker.book.title(),author:e.faker.book.author(),genre:e.faker.book.genre(),publisher:e.faker.book.publisher(),series:e.faker.book.series()},science:{chemicalElement:e.faker.science.chemicalElement(),unit:e.faker.science.unit()},system:{fileName:e.faker.system.fileName(),mimeType:e.faker.system.mimeType(),fileType:e.faker.system.fileType(),directoryPath:e.faker.system.directoryPath()},airline:{aircraftType:e.faker.airline.aircraftType(),airplane:e.faker.airline.airplane(),airport:e.faker.airline.airport(),seat:e.faker.airline.seat()}});return{records:t,analytics:{totalRecords:t.length,uniqueGenres:[...new Set(t.map(a=>a.music.genre))],averageContentLength:t.reduce((a,s)=>a+s.content.paragraph.length,0)/t.length}}}function o(i=15){const t=[];for(let a=0;a<i;a++)t.push({id:e.faker.string.uuid(),animal:{type:e.faker.animal.type(),dog:e.faker.animal.dog(),cat:e.faker.animal.cat(),bird:e.faker.animal.bird(),fish:e.faker.animal.fish(),horse:e.faker.animal.horse(),bear:e.faker.animal.bear(),cetacean:e.faker.animal.cetacean(),cow:e.faker.animal.cow(),crocodilia:e.faker.animal.crocodilia(),insect:e.faker.animal.insect(),lion:e.faker.animal.lion(),rabbit:e.faker.animal.rabbit(),rodent:e.faker.animal.rodent(),snake:e.faker.animal.snake()},food:{adjective:e.faker.food.adjective(),description:e.faker.food.description(),dish:e.faker.food.dish(),ethnicCategory:e.faker.food.ethnicCategory(),fruit:e.faker.food.fruit(),ingredient:e.faker.food.ingredient(),meat:e.faker.food.meat(),spice:e.faker.food.spice(),vegetable:e.faker.food.vegetable()},hacker:{abbreviation:e.faker.hacker.abbreviation(),adjective:e.faker.hacker.adjective(),ingverb:e.faker.hacker.ingverb(),noun:e.faker.hacker.noun(),phrase:e.faker.hacker.phrase(),verb:e.faker.hacker.verb()}});return{data:t,summary:{totalEntries:t.length,uniqueAnimalTypes:[...new Set(t.map(a=>a.animal.type))],uniqueFoodCategories:[...new Set(t.map(a=>a.food.ethnicCategory))]}}}function c(i=25){const t=[];for(let a=0;a<i;a++)t.push({id:e.faker.string.uuid(),git:{branch:e.faker.git.branch(),commitEntry:e.faker.git.commitEntry(),commitMessage:e.faker.git.commitMessage(),commitSha:e.faker.git.commitSha()},phone:{number:e.faker.phone.number(),imei:e.faker.phone.imei()},word:{adjective:e.faker.word.adjective(),adverb:e.faker.word.adverb(),conjunction:e.faker.word.conjunction(),interjection:e.faker.word.interjection(),noun:e.faker.word.noun(),preposition:e.faker.word.preposition(),verb:e.faker.word.verb(),words:e.faker.word.words(5)},database:{column:e.faker.database.column(),type:e.faker.database.type(),collation:e.faker.database.collation(),engine:e.faker.database.engine(),mongodbObjectId:e.faker.database.mongodbObjectId()},datatype:{boolean:e.faker.datatype.boolean(),float:e.faker.number.float(),hexadecimal:e.faker.string.hexadecimal(),number:e.faker.number.int(),uuid:e.faker.string.uuid(),json:JSON.stringify({key:e.faker.lorem.word(),value:e.faker.number.int(),active:e.faker.datatype.boolean()})}});return{data:t,metadata:{totalRecords:t.length,generatedAt:new Date,fakerModulesUsed:["git","phone","word","database","datatype","string","helpers","number"]}}}return g}var R;function W(){return R||(R=1,function(e){Object.defineProperty(e,"__esModule",{value:!0});const n=P;n.__exportStar(E(),e),n.__exportStar(C(),e),n.__exportStar(C(),e),n.__exportStar(Q(),e)}(U)),U}var j=W();function Y(){const[e,n]=_.useState("light"),[d,m]=_.useState(null),o=()=>{const t=new j.CreateUserDto;return t.name="Test User",t.email="<EMAIL>",t.age=25,t},c=()=>{const t=new j.UserResponseDto;return t.id="123e4567-e89b-12d3-a456-************",t.name="Test User",t.email="<EMAIL>",t.age=25,t.createdAt=new Date,t},i=()=>{console.log("🚀 Generating heavy fake data...");const t=j.generateComplexFakeData(3),a=j.generateFakeEcommerceData(2);m({complex:t,ecommerce:a}),console.log("✅ Heavy data generated:",{complexCount:t.users.length,ecommerceCount:a.products.length})};return r.jsxs("div",{style:{padding:"20px",border:"1px solid #ccc",margin:"10px"},children:[r.jsx("h3",{children:"Tree-Shaking Test Component"}),r.jsx("p",{children:"This component imports heavy utilities from @my-nx/common."}),r.jsx("p",{children:"Use the buttons below to test different scenarios:"}),r.jsxs("div",{style:{marginBottom:"20px"},children:[r.jsx("button",{onClick:()=>n("light"),style:{marginRight:"10px",padding:"8px 16px",backgroundColor:e==="light"?"#007bff":"#f8f9fa",color:e==="light"?"white":"black",border:"1px solid #ccc",cursor:"pointer"},children:"Light Mode (Tree-shakeable)"}),r.jsx("button",{onClick:()=>{n("heavy"),i()},style:{padding:"8px 16px",backgroundColor:e==="heavy"?"#dc3545":"#f8f9fa",color:e==="heavy"?"white":"black",border:"1px solid #ccc",cursor:"pointer"},children:"Heavy Mode (Runtime imports)"})]}),e==="light"&&r.jsxs("div",{children:[r.jsx("h4",{children:"✅ Used DTOs (should be in bundle):"}),r.jsx("pre",{style:{background:"#f5f5f5",padding:"10px",fontSize:"12px"},children:JSON.stringify(o(),null,2)}),r.jsx("pre",{style:{background:"#f5f5f5",padding:"10px",fontSize:"12px"},children:JSON.stringify(c(),null,2)}),r.jsx("h4",{children:"🌳 Imported but unused (should be tree-shaken):"}),r.jsxs("ul",{children:[r.jsx("li",{children:"generateComplexFakeData"}),r.jsx("li",{children:"generateFakeEcommerceData"}),r.jsx("li",{children:"generateFakeDatabaseRecords"}),r.jsx("li",{children:"generateFakeAnimalFoodData"}),r.jsx("li",{children:"generateComprehensiveFakeData"})]}),r.jsxs("p",{style:{color:"#666",fontSize:"14px"},children:[r.jsx("strong",{children:"These functions and faker should NOT be in the bundle!"}),r.jsx("br",{}),"They are imported but never called in light mode."]})]}),e==="heavy"&&d&&r.jsxs("div",{children:[r.jsx("h4",{children:"🔥 Heavy Data Generated (runtime imports):"}),r.jsxs("div",{style:{maxHeight:"300px",overflow:"auto",background:"#f5f5f5",padding:"10px"},children:[r.jsxs("h5",{children:["Complex Data (",d.complex.users.length," users):"]}),r.jsx("pre",{style:{fontSize:"10px"},children:JSON.stringify(d.complex.users[0],null,2)}),r.jsxs("h5",{children:["E-commerce Data (",d.ecommerce.products.length," products):"]}),r.jsx("pre",{style:{fontSize:"10px"},children:JSON.stringify(d.ecommerce.products[0],null,2)})]}),r.jsx("p",{style:{color:"#666",fontSize:"12px",marginTop:"10px"},children:"In heavy mode, the faker functions are actually called, so they will be included in the bundle. This demonstrates the difference between imported-but-unused vs imported-and-used code."})]})]})}function ee(){const[e,n]=_.useState([]),[d,m]=_.useState(!1),[o,c]=_.useState(null),i=async()=>{try{m(!0),c(null);const a=await S.getUsers();n(a)}catch(a){c("Failed to load users. Make sure the backend is running on port 3000."),console.error("Error loading users:",a)}finally{m(!1)}},t=async()=>{try{const a={name:"New User",email:`user${Date.now()}@example.com`,age:28};await S.createUser(a),await i()}catch(a){c("Failed to create user"),console.error("Error creating user:",a)}};return _.useEffect(()=>{i()},[]),r.jsxs("div",{style:{padding:"20px"},children:[r.jsx("h1",{children:"NX Monorepo Demo - Frontend"}),r.jsx("p",{children:"This frontend uses shared DTOs from the common package and communicates with the NestJS backend via axios."}),r.jsx("div",{role:"navigation",style:{marginBottom:"20px"},children:r.jsxs("ul",{style:{display:"flex",listStyle:"none",gap:"20px",padding:0},children:[r.jsx("li",{children:r.jsx(v,{to:"/",children:"Users"})}),r.jsx("li",{children:r.jsx(v,{to:"/about",children:"About"})})]})}),r.jsxs(M,{children:[r.jsx(w,{path:"/",element:r.jsxs("div",{children:[r.jsx("h2",{children:"Users Management"}),r.jsxs("div",{style:{marginBottom:"20px"},children:[r.jsx("button",{onClick:i,disabled:d,children:d?"Loading...":"Refresh Users"}),r.jsx("button",{onClick:t,style:{marginLeft:"10px"},children:"Add Sample User"})]}),o&&r.jsx("div",{style:{color:"red",marginBottom:"20px"},children:o}),r.jsxs("div",{children:[r.jsx("h3",{children:"Users List:"}),e.length===0?r.jsxs("p",{children:["No users found."," ",!o&&"Try adding a sample user or check if the backend is running."]}):r.jsx("ul",{children:e.map(a=>r.jsxs("li",{style:{marginBottom:"10px",padding:"10px",border:"1px solid #ccc"},children:[r.jsx("strong",{children:a.name})," (",a.email,")",a.age&&r.jsxs("span",{children:[" - Age: ",a.age]}),r.jsx("br",{}),r.jsxs("small",{children:["Created:"," ",new Date(a.createdAt).toLocaleDateString()]})]},a.id))})]})]})}),r.jsx(w,{path:"/about",element:r.jsxs("div",{children:[r.jsx("h2",{children:"About This Demo"}),r.jsx("p",{children:"This is a demonstration of an NX monorepo with:"}),r.jsxs("ul",{children:[r.jsx("li",{children:"✅ NX v20 with pnpm workspaces"}),r.jsx("li",{children:"✅ Shared common package with DTOs"}),r.jsx("li",{children:"✅ NestJS backend with TypeScript and webpack"}),r.jsx("li",{children:"✅ Vite React frontend"}),r.jsx("li",{children:"✅ Axios for API communication"}),r.jsx("li",{children:"✅ NestJS Swagger for API documentation"}),r.jsx("li",{children:"✅ Class-validator for request validation"}),r.jsx("li",{children:"✅ Reflect-metadata for decorators"}),r.jsx("li",{children:"✅ Tree-shaking working with faker"})]}),r.jsx(Y,{}),r.jsxs("p",{children:[r.jsx("strong",{children:"Backend:"})," ",r.jsx("a",{href:"http://localhost:3000/api/docs",target:"_blank",rel:"noopener noreferrer",children:"Swagger API Documentation"})]}),r.jsx(v,{to:"/",children:"← Back to Users"})]})})]})]})}const re=L.createRoot(document.getElementById("root"));re.render(r.jsx(_.StrictMode,{children:r.jsx($,{children:r.jsx(ee,{})})}));
