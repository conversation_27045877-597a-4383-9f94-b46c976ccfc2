/// <reference types='vitest' />
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { visualizer } from 'rollup-plugin-visualizer';

export default defineConfig(() => ({
  root: __dirname,
  cacheDir: '../../node_modules/.vite/apps/frontend',
  server: {
    port: 4200,
    host: 'localhost',
  },
  preview: {
    port: 4200,
    host: 'localhost',
  },
  define: {
    // Replace global require function for problematic modules
    global: 'globalThis',
    'process.env.NODE_ENV': '"production"',
  },

  plugins: [
    react(),
    visualizer({
      filename: 'dist/bundle-analysis.html',
      open: false,
      gzipSize: true,
      brotliSize: true,
    }),
    // Backend dependency stubbing plugin - works better in dev mode
    {
      name: 'backend-dependency-stubs',
      enforce: 'pre',
      resolveId(id) {
        const backendDeps = [
          'class-validator',
          '@faker-js/faker',
          'reflect-metadata',
          '@nestjs/swagger',
          '@nestjs/common',
          '@nestjs/core',
          '@nestjs/platform-express',
          '@nestjs/mapped-types',
        ];

        if (backendDeps.includes(id)) {
          return id; // Handle in load hook
        }
        return null;
      },
      load(id) {
        if (id === 'class-validator') {
          return `
            // Class-validator stub for frontend
            export const validate = () => Promise.resolve([]);
            export const IsString = () => () => {};
            export const IsEmail = () => () => {};
            export const IsOptional = () => () => {};
            export const IsNumber = () => () => {};
            export const Min = () => () => {};
            export const IsUUID = () => () => {};
            export const IsDateString = () => () => {};
            export default { validate, IsString, IsEmail, IsOptional, IsNumber, Min, IsUUID, IsDateString };
          `;
        }

        if (id === '@faker-js/faker') {
          return `
            // Faker stub for frontend
            const createProxy = () => new Proxy({}, {
              get: (target, prop) => {
                if (typeof prop === 'string') {
                  return createProxy();
                }
                return undefined;
              }
            });
            export const faker = createProxy();
            export default { faker };
          `;
        }

        if (id === 'reflect-metadata') {
          return `
            // Reflect-metadata stub for frontend
            export default {};
          `;
        }

        if (id === '@nestjs/swagger') {
          return `
            // NestJS Swagger stub for frontend
            export const ApiProperty = () => () => {};
            export default { ApiProperty };
          `;
        }

        if (
          id === '@nestjs/common' ||
          id === '@nestjs/core' ||
          id === '@nestjs/platform-express' ||
          id === '@nestjs/mapped-types'
        ) {
          return `
            // NestJS stub for frontend
            export default {};
          `;
        }

        return null;
      },
    },
  ],
  resolve: {
    alias: [
      // Fix for @nestjs/mapped-types trying to import class-transformer/storage
      { find: 'class-transformer/storage', replacement: 'class-transformer' },
      // Create virtual modules for backend dependencies
      // Note: class-validator is handled by the custom plugin above
      {
        find: /^@faker-js\/faker$/,
        replacement:
          'data:text/javascript,export const faker = {};export default {};',
      },
      {
        find: /^@nestjs\/swagger$/,
        replacement:
          'data:text/javascript,export const ApiProperty = () => () => {};export default {};',
      },
      {
        find: /^@nestjs\/common$/,
        replacement: 'data:text/javascript,export default {};',
      },
      {
        find: /^@nestjs\/core$/,
        replacement: 'data:text/javascript,export default {};',
      },
      {
        find: /^@nestjs\/platform-express$/,
        replacement: 'data:text/javascript,export default {};',
      },
      {
        find: /^@nestjs\/mapped-types$/,
        replacement: 'data:text/javascript,export default {};',
      },
      {
        find: /^reflect-metadata$/,
        replacement: 'data:text/javascript,export default {};',
      },
    ],
  },
  optimizeDeps: {
    include: ['@my-nx/common'],
  },
  build: {
    outDir: './dist',
    emptyOutDir: true,
    reportCompressedSize: true,
    commonjsOptions: {
      transformMixedEsModules: true,
      include: [/node_modules/, /packages\/common/],
    },
    rollupOptions: {
      external: [],
      treeshake: {
        moduleSideEffects: false,
        propertyReadSideEffects: false,
        tryCatchDeoptimization: false,
      },
      output: {
        manualChunks: (id) => {
          // Separate chunks for debugging
          if (id.includes('@faker-js/faker')) {
            return 'faker';
          }
          if (id.includes('@my-nx/common')) {
            return 'common';
          }
          if (id.includes('class-transformer')) {
            return 'class-transformer';
          }
          if (id.includes('nest')) {
            return 'nest';
          }
          if (id.includes('node_modules')) {
            return 'vendor';
          }
          return undefined;
        },
      },
    },
  },
  test: {
    watch: false,
    globals: true,
    environment: 'jsdom',
    include: ['{src,tests}/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}'],
    reporters: ['default'],
    coverage: {
      reportsDirectory: './test-output/vitest/coverage',
      provider: 'v8' as const,
    },
  },
}));
