{"name": "@./source", "version": "0.0.0", "license": "MIT", "scripts": {}, "private": true, "devDependencies": {"@eslint/js": "^9.8.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.0", "@nx/devkit": "21.2.1", "@nx/eslint": "20.8.2", "@nx/eslint-plugin": "20.8.2", "@nx/jest": "20.8.2", "@nx/js": "21.2.1", "@nx/nest": "^21.2.1", "@nx/node": "21.2.1", "@nx/playwright": "21.2.1", "@nx/react": "^21.2.1", "@nx/vite": "21.2.1", "@nx/web": "21.2.1", "@nx/webpack": "21.2.1", "@playwright/test": "^1.36.0", "@swc-node/register": "~1.9.1", "@swc/cli": "~0.6.0", "@swc/core": "~1.5.7", "@swc/helpers": "~0.5.11", "@swc/jest": "~0.2.36", "@testing-library/dom": "10.4.0", "@testing-library/react": "16.1.0", "@types/jest": "^29.5.12", "@types/node": "^20.0.0", "@types/react": "19.0.0", "@types/react-dom": "19.0.0", "@vitejs/plugin-react": "^4.2.0", "@vitest/coverage-v8": "^3.0.5", "@vitest/ui": "^3.0.0", "eslint": "^9.8.0", "eslint-config-prettier": "^10.0.0", "eslint-plugin-import": "2.31.0", "eslint-plugin-jsx-a11y": "6.10.1", "eslint-plugin-playwright": "^1.6.2", "eslint-plugin-react": "7.35.0", "eslint-plugin-react-hooks": "5.0.0", "jest": "^29.7.0", "jest-environment-node": "^29.7.0", "jiti": "2.4.2", "jsdom": "~22.1.0", "jsonc-eslint-parser": "^2.1.0", "nx": "20.8.2", "prettier": "^2.6.2", "ts-jest": "^29.1.0", "ts-node": "10.9.1", "tslib": "^2.3.0", "typescript": "~5.7.2", "typescript-eslint": "^8.19.0", "verdaccio": "^6.0.5", "vite": "^6.0.0", "vitest": "^3.0.0", "webpack-cli": "^5.1.4"}, "nx": {"includedScripts": [], "targets": {"local-registry": {"executor": "@nx/js:verda<PERSON>o", "options": {"port": 4873, "config": ".verdaccio/config.yml", "storage": "tmp/local-registry/storage"}}}}, "dependencies": {"@nestjs/common": "^11.0.0", "@nestjs/core": "^11.0.0", "@nestjs/platform-express": "^11.0.0", "axios": "^1.6.0", "class-transformer": "^0.5.1", "react": "19.0.0", "react-dom": "19.0.0", "react-router-dom": "6.29.0", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.0"}}