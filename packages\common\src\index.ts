// Tree-shakeable exports for shared DTOs and utilities
// Frontend will only bundle what it imports, backend gets everything

// Core DTOs - these are the main shared types
export * from './lib/common.js';

// Transformation utilities - these will only be bundled if imported
export * from './lib/transformers.js';

// Validation utilities - these will only be bundled if imported (backend only)
export * from './lib/transformers.js';

// Heavy utilities - these will only be bundled if imported
export * from './lib/heavy-utils.js';
