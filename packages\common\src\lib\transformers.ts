// Transformation utilities with static imports
import { CreateUserDto, UpdateUserDto, UserResponseDto } from './common.js';
import { plainToInstance } from 'class-transformer';
import { validate } from 'class-validator';
import 'reflect-metadata';

/**
 * Lightweight transformation utilities for frontend use
 * These don't require class-transformer or class-validator
 */

/**
 * Simple object assignment transformation to UserResponseDto
 * This is lightweight and doesn't require class-transformer
 */
export function transformToUserResponseDto(plainObject: any): UserResponseDto {
  const dto = new UserResponseDto();
  Object.assign(dto, plainObject);
  if (plainObject.createdAt && typeof plainObject.createdAt === 'string') {
    dto.createdAt = new Date(plainObject.createdAt);
  }
  return dto;
}

/**
 * Transform array of plain objects to UserResponseDto array
 */
export function transformToUserResponseDtoArray(
  plainObjects: any[]
): UserResponseDto[] {
  return plainObjects.map((obj) => transformToUserResponseDto(obj));
}

/**
 * Convert UserResponseDto to plain object for JSON serialization
 */
export function serializeUserResponse(userDto: UserResponseDto): any {
  return {
    id: userDto.id,
    name: userDto.name,
    email: userDto.email,
    age: userDto.age,
    createdAt: userDto.createdAt,
  };
}

// Heavy validation utilities - these will only be bundled if imported (backend only)
// These require class-transformer and class-validator

/**
 * Transform plain object to DTO instance and validate it
 * @param dtoClass - The DTO class to transform to
 * @param plainObject - The plain object to transform
 * @returns Promise with the transformed and validated DTO instance
 * @throws ValidationError if validation fails
 */
export async function transformAndValidate<T extends object>(
  dtoClass: new () => T,
  plainObject: any
): Promise<T> {
  // Transform plain object to class instance
  const dtoInstance = plainToInstance(dtoClass, plainObject);

  // Validate the instance
  const errors = await validate(dtoInstance);

  if (errors.length > 0) {
    // Format validation errors into a readable message
    const errorMessages = errors
      .map((error) => Object.values(error.constraints || {}).join(', '))
      .join('; ');
    throw new Error(`Validation failed: ${errorMessages}`);
  }

  return dtoInstance;
}

/**
 * Transform plain object to CreateUserDto with validation
 */
export async function transformToCreateUserDto(
  plainObject: any
): Promise<CreateUserDto> {
  return transformAndValidate(CreateUserDto, plainObject);
}

/**
 * Transform plain object to UpdateUserDto with validation
 */
export async function transformToUpdateUserDto(
  plainObject: any
): Promise<UpdateUserDto> {
  return transformAndValidate(UpdateUserDto, plainObject);
}
