function ln(r){return r&&r.__esModule&&Object.prototype.hasOwnProperty.call(r,"default")?r.default:r}function R(r){if(Object.prototype.hasOwnProperty.call(r,"__esModule"))return r;var n=r.default;if(typeof n=="function"){var t=function e(){return this instanceof e?Reflect.construct(n,arguments,this.constructor):n.apply(this,arguments)};t.prototype=n.prototype}else t={};return Object.defineProperty(t,"__esModule",{value:!0}),Object.keys(r).forEach(function(e){var o=Object.getOwnPropertyDescriptor(r,e);Object.defineProperty(t,e,o.get?o:{enumerable:!0,get:function(){return r[e]}})}),t}var p;(function(r){r[r.PLAIN_TO_CLASS=0]="PLAIN_TO_CLASS",r[r.CLASS_TO_PLAIN=1]="CLASS_TO_PLAIN",r[r.CLASS_TO_CLASS=2]="CLASS_TO_CLASS"})(p||(p={}));var J=function(){function r(){this._typeMetadatas=new Map,this._transformMetadatas=new Map,this._exposeMetadatas=new Map,this._excludeMetadatas=new Map,this._ancestorsMap=new Map}return r.prototype.addTypeMetadata=function(n){this._typeMetadatas.has(n.target)||this._typeMetadatas.set(n.target,new Map),this._typeMetadatas.get(n.target).set(n.propertyName,n)},r.prototype.addTransformMetadata=function(n){this._transformMetadatas.has(n.target)||this._transformMetadatas.set(n.target,new Map),this._transformMetadatas.get(n.target).has(n.propertyName)||this._transformMetadatas.get(n.target).set(n.propertyName,[]),this._transformMetadatas.get(n.target).get(n.propertyName).push(n)},r.prototype.addExposeMetadata=function(n){this._exposeMetadatas.has(n.target)||this._exposeMetadatas.set(n.target,new Map),this._exposeMetadatas.get(n.target).set(n.propertyName,n)},r.prototype.addExcludeMetadata=function(n){this._excludeMetadatas.has(n.target)||this._excludeMetadatas.set(n.target,new Map),this._excludeMetadatas.get(n.target).set(n.propertyName,n)},r.prototype.findTransformMetadatas=function(n,t,e){return this.findMetadatas(this._transformMetadatas,n,t).filter(function(o){return!o.options||o.options.toClassOnly===!0&&o.options.toPlainOnly===!0?!0:o.options.toClassOnly===!0?e===p.CLASS_TO_CLASS||e===p.PLAIN_TO_CLASS:o.options.toPlainOnly===!0?e===p.CLASS_TO_PLAIN:!0})},r.prototype.findExcludeMetadata=function(n,t){return this.findMetadata(this._excludeMetadatas,n,t)},r.prototype.findExposeMetadata=function(n,t){return this.findMetadata(this._exposeMetadatas,n,t)},r.prototype.findExposeMetadataByCustomName=function(n,t){return this.getExposedMetadatas(n).find(function(e){return e.options&&e.options.name===t})},r.prototype.findTypeMetadata=function(n,t){return this.findMetadata(this._typeMetadatas,n,t)},r.prototype.getStrategy=function(n){var t=this._excludeMetadatas.get(n),e=t&&t.get(void 0),o=this._exposeMetadatas.get(n),u=o&&o.get(void 0);return e&&u||!e&&!u?"none":e?"excludeAll":"exposeAll"},r.prototype.getExposedMetadatas=function(n){return this.getMetadata(this._exposeMetadatas,n)},r.prototype.getExcludedMetadatas=function(n){return this.getMetadata(this._excludeMetadatas,n)},r.prototype.getExposedProperties=function(n,t){return this.getExposedMetadatas(n).filter(function(e){return!e.options||e.options.toClassOnly===!0&&e.options.toPlainOnly===!0?!0:e.options.toClassOnly===!0?t===p.CLASS_TO_CLASS||t===p.PLAIN_TO_CLASS:e.options.toPlainOnly===!0?t===p.CLASS_TO_PLAIN:!0}).map(function(e){return e.propertyName})},r.prototype.getExcludedProperties=function(n,t){return this.getExcludedMetadatas(n).filter(function(e){return!e.options||e.options.toClassOnly===!0&&e.options.toPlainOnly===!0?!0:e.options.toClassOnly===!0?t===p.CLASS_TO_CLASS||t===p.PLAIN_TO_CLASS:e.options.toPlainOnly===!0?t===p.CLASS_TO_PLAIN:!0}).map(function(e){return e.propertyName})},r.prototype.clear=function(){this._typeMetadatas.clear(),this._exposeMetadatas.clear(),this._excludeMetadatas.clear(),this._ancestorsMap.clear()},r.prototype.getMetadata=function(n,t){var e=n.get(t),o;e&&(o=Array.from(e.values()).filter(function(y){return y.propertyName!==void 0}));for(var u=[],s=0,a=this.getAncestors(t);s<a.length;s++){var f=a[s],l=n.get(f);if(l){var c=Array.from(l.values()).filter(function(y){return y.propertyName!==void 0});u.push.apply(u,c)}}return u.concat(o||[])},r.prototype.findMetadata=function(n,t,e){var o=n.get(t);if(o){var u=o.get(e);if(u)return u}for(var s=0,a=this.getAncestors(t);s<a.length;s++){var f=a[s],l=n.get(f);if(l){var c=l.get(e);if(c)return c}}},r.prototype.findMetadatas=function(n,t,e){var o=n.get(t),u;o&&(u=o.get(e));for(var s=[],a=0,f=this.getAncestors(t);a<f.length;a++){var l=f[a],c=n.get(l);c&&c.has(e)&&s.push.apply(s,c.get(e))}return s.slice().reverse().concat((u||[]).slice().reverse())},r.prototype.getAncestors=function(n){if(!n)return[];if(!this._ancestorsMap.has(n)){for(var t=[],e=Object.getPrototypeOf(n.prototype.constructor);typeof e.prototype<"u";e=Object.getPrototypeOf(e.prototype.constructor))t.push(e);this._ancestorsMap.set(n,t)}return this._ancestorsMap.get(n)},r}(),m=new J;function U(){if(typeof globalThis<"u"||typeof globalThis<"u")return globalThis;if(typeof window<"u")return window;if(typeof self<"u")return self}function $(r){return r!==null&&typeof r=="object"&&typeof r.then=="function"}var B=function(r,n,t){if(t||arguments.length===2)for(var e=0,o=n.length,u;e<o;e++)(u||!(e in n))&&(u||(u=Array.prototype.slice.call(n,0,e)),u[e]=n[e]);return r.concat(u||Array.prototype.slice.call(n))};function q(r){var n=new r;return!(n instanceof Set)&&!("push"in n)?[]:n}var _=function(){function r(n,t){this.transformationType=n,this.options=t,this.recursionStack=new Set}return r.prototype.transform=function(n,t,e,o,u,s){var a=this;if(s===void 0&&(s=0),Array.isArray(t)||t instanceof Set){var f=o&&this.transformationType===p.PLAIN_TO_CLASS?q(o):[];return t.forEach(function(S,T){var C=n?n[T]:void 0;if(!a.options.enableCircularCheck||!a.isCircular(S)){var g=void 0;if(typeof e!="function"&&e&&e.options&&e.options.discriminator&&e.options.discriminator.property&&e.options.discriminator.subTypes){if(a.transformationType===p.PLAIN_TO_CLASS){g=e.options.discriminator.subTypes.find(function(v){return v.name===S[e.options.discriminator.property]});var L={newObject:f,object:S,property:void 0},d=e.typeFunction(L);g===void 0?g=d:g=g.value,e.options.keepDiscriminatorProperty||delete S[e.options.discriminator.property]}a.transformationType===p.CLASS_TO_CLASS&&(g=S.constructor),a.transformationType===p.CLASS_TO_PLAIN&&(S[e.options.discriminator.property]=e.options.discriminator.subTypes.find(function(v){return v.value===S.constructor}).name)}else g=e;var M=a.transform(C,S,g,void 0,S instanceof Map,s+1);f instanceof Set?f.add(M):f.push(M)}else a.transformationType===p.CLASS_TO_CLASS&&(f instanceof Set?f.add(S):f.push(S))}),f}else{if(e===String&&!u)return t==null?t:String(t);if(e===Number&&!u)return t==null?t:Number(t);if(e===Boolean&&!u)return t==null?t:!!t;if((e===Date||t instanceof Date)&&!u)return t instanceof Date?new Date(t.valueOf()):t==null?t:new Date(t);if(U().Buffer&&(e===Buffer||t instanceof Buffer)&&!u)return t==null?t:Buffer.from(t);if($(t)&&!u)return new Promise(function(S,T){t.then(function(C){return S(a.transform(void 0,C,e,void 0,void 0,s+1))},T)});if(!u&&t!==null&&typeof t=="object"&&typeof t.then=="function")return t;if(typeof t=="object"&&t!==null){!e&&t.constructor!==Object&&(!Array.isArray(t)&&t.constructor===Array||(e=t.constructor)),!e&&n&&(e=n.constructor),this.options.enableCircularCheck&&this.recursionStack.add(t);var l=this.getKeys(e,t,u),c=n||{};!n&&(this.transformationType===p.PLAIN_TO_CLASS||this.transformationType===p.CLASS_TO_CLASS)&&(u?c=new Map:e?c=new e:c={});for(var y=function(S){if(S==="__proto__"||S==="constructor")return"continue";var T=S,C=S,g=S;if(!i.options.ignoreDecorators&&e){if(i.transformationType===p.PLAIN_TO_CLASS){var L=m.findExposeMetadataByCustomName(e,S);L&&(g=L.propertyName,C=L.propertyName)}else if(i.transformationType===p.CLASS_TO_PLAIN||i.transformationType===p.CLASS_TO_CLASS){var L=m.findExposeMetadata(e,S);L&&L.options&&L.options.name&&(C=L.options.name)}}var d=void 0;i.transformationType===p.PLAIN_TO_CLASS?d=t[T]:t instanceof Map?d=t.get(T):t[T]instanceof Function?d=t[T]():d=t[T];var M=void 0,v=d instanceof Map;if(e&&u)M=e;else if(e){var A=m.findTypeMetadata(e,g);if(A){var K={newObject:c,object:t,property:g},j=A.typeFunction?A.typeFunction(K):A.reflectedType;A.options&&A.options.discriminator&&A.options.discriminator.property&&A.options.discriminator.subTypes?t[T]instanceof Array?M=A:(i.transformationType===p.PLAIN_TO_CLASS&&(M=A.options.discriminator.subTypes.find(function(P){if(d&&d instanceof Object&&A.options.discriminator.property in d)return P.name===d[A.options.discriminator.property]}),M===void 0?M=j:M=M.value,A.options.keepDiscriminatorProperty||d&&d instanceof Object&&A.options.discriminator.property in d&&delete d[A.options.discriminator.property]),i.transformationType===p.CLASS_TO_CLASS&&(M=d.constructor),i.transformationType===p.CLASS_TO_PLAIN&&d&&(d[A.options.discriminator.property]=A.options.discriminator.subTypes.find(function(P){return P.value===d.constructor}).name)):M=j,v=v||A.reflectedType===Map}else if(i.options.targetMaps)i.options.targetMaps.filter(function(P){return P.target===e&&!!P.properties[g]}).forEach(function(P){return M=P.properties[g]});else if(i.options.enableImplicitConversion&&i.transformationType===p.PLAIN_TO_CLASS){var w=Reflect.getMetadata("design:type",e.prototype,g);w&&(M=w)}}var D=Array.isArray(t[T])?i.getReflectedType(e,g):void 0,z=n?n[T]:void 0;if(c.constructor.prototype){var k=Object.getOwnPropertyDescriptor(c.constructor.prototype,C);if((i.transformationType===p.PLAIN_TO_CLASS||i.transformationType===p.CLASS_TO_CLASS)&&(k&&!k.set||c[C]instanceof Function))return"continue"}if(!i.options.enableCircularCheck||!i.isCircular(d)){var E=i.transformationType===p.PLAIN_TO_CLASS?C:S,h=void 0;i.transformationType===p.CLASS_TO_PLAIN?(h=t[E],h=i.applyCustomTransformations(h,e,E,t,i.transformationType),h=t[E]===h?d:h,h=i.transform(z,h,M,D,v,s+1)):d===void 0&&i.options.exposeDefaultValues?h=c[C]:(h=i.transform(z,d,M,D,v,s+1),h=i.applyCustomTransformations(h,e,E,t,i.transformationType)),(h!==void 0||i.options.exposeUnsetFields)&&(c instanceof Map?c.set(C,h):c[C]=h)}else if(i.transformationType===p.CLASS_TO_CLASS){var h=d;h=i.applyCustomTransformations(h,e,S,t,i.transformationType),(h!==void 0||i.options.exposeUnsetFields)&&(c instanceof Map?c.set(C,h):c[C]=h)}},i=this,b=0,F=l;b<F.length;b++){var G=F[b];y(G)}return this.options.enableCircularCheck&&this.recursionStack.delete(t),c}else return t}},r.prototype.applyCustomTransformations=function(n,t,e,o,u){var s=this,a=m.findTransformMetadatas(t,e,this.transformationType);return this.options.version!==void 0&&(a=a.filter(function(f){return f.options?s.checkVersion(f.options.since,f.options.until):!0})),this.options.groups&&this.options.groups.length?a=a.filter(function(f){return f.options?s.checkGroups(f.options.groups):!0}):a=a.filter(function(f){return!f.options||!f.options.groups||!f.options.groups.length}),a.forEach(function(f){n=f.transformFn({value:n,key:e,obj:o,type:u,options:s.options})}),n},r.prototype.isCircular=function(n){return this.recursionStack.has(n)},r.prototype.getReflectedType=function(n,t){if(n){var e=m.findTypeMetadata(n,t);return e?e.reflectedType:void 0}},r.prototype.getKeys=function(n,t,e){var o=this,u=m.getStrategy(n);u==="none"&&(u=this.options.strategy||"exposeAll");var s=[];if((u==="exposeAll"||e)&&(t instanceof Map?s=Array.from(t.keys()):s=Object.keys(t)),e)return s;if(this.options.ignoreDecorators&&this.options.excludeExtraneousValues&&n){var a=m.getExposedProperties(n,this.transformationType),f=m.getExcludedProperties(n,this.transformationType);s=B(B([],a,!0),f,!0)}if(!this.options.ignoreDecorators&&n){var a=m.getExposedProperties(n,this.transformationType);this.transformationType===p.PLAIN_TO_CLASS&&(a=a.map(function(y){var i=m.findExposeMetadata(n,y);return i&&i.options&&i.options.name?i.options.name:y})),this.options.excludeExtraneousValues?s=a:s=s.concat(a);var l=m.getExcludedProperties(n,this.transformationType);l.length>0&&(s=s.filter(function(y){return!l.includes(y)})),this.options.version!==void 0&&(s=s.filter(function(y){var i=m.findExposeMetadata(n,y);return!i||!i.options?!0:o.checkVersion(i.options.since,i.options.until)})),this.options.groups&&this.options.groups.length?s=s.filter(function(y){var i=m.findExposeMetadata(n,y);return!i||!i.options?!0:o.checkGroups(i.options.groups)}):s=s.filter(function(y){var i=m.findExposeMetadata(n,y);return!i||!i.options||!i.options.groups||!i.options.groups.length})}return this.options.excludePrefixes&&this.options.excludePrefixes.length&&(s=s.filter(function(c){return o.options.excludePrefixes.every(function(y){return c.substr(0,y.length)!==y})})),s=s.filter(function(c,y,i){return i.indexOf(c)===y}),s},r.prototype.checkVersion=function(n,t){var e=!0;return e&&n&&(e=this.options.version>=n),e&&t&&(e=this.options.version<t),e},r.prototype.checkGroups=function(n){return n?this.options.groups.some(function(t){return n.includes(t)}):!0},r}(),N={enableCircularCheck:!1,enableImplicitConversion:!1,excludeExtraneousValues:!1,excludePrefixes:void 0,exposeDefaultValues:!1,exposeUnsetFields:!0,groups:void 0,ignoreDecorators:!1,strategy:void 0,targetMaps:void 0,version:void 0},O=function(){return O=Object.assign||function(r){for(var n,t=1,e=arguments.length;t<e;t++){n=arguments[t];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(r[o]=n[o])}return r},O.apply(this,arguments)},I=function(){function r(){}return r.prototype.instanceToPlain=function(n,t){var e=new _(p.CLASS_TO_PLAIN,O(O({},N),t));return e.transform(void 0,n,void 0,void 0,void 0,void 0)},r.prototype.classToPlainFromExist=function(n,t,e){var o=new _(p.CLASS_TO_PLAIN,O(O({},N),e));return o.transform(t,n,void 0,void 0,void 0,void 0)},r.prototype.plainToInstance=function(n,t,e){var o=new _(p.PLAIN_TO_CLASS,O(O({},N),e));return o.transform(void 0,t,n,void 0,void 0,void 0)},r.prototype.plainToClassFromExist=function(n,t,e){var o=new _(p.PLAIN_TO_CLASS,O(O({},N),e));return o.transform(n,t,void 0,void 0,void 0,void 0)},r.prototype.instanceToInstance=function(n,t){var e=new _(p.CLASS_TO_CLASS,O(O({},N),t));return e.transform(void 0,n,void 0,void 0,void 0,void 0)},r.prototype.classToClassFromExist=function(n,t,e){var o=new _(p.CLASS_TO_CLASS,O(O({},N),e));return o.transform(t,n,void 0,void 0,void 0,void 0)},r.prototype.serialize=function(n,t){return JSON.stringify(this.instanceToPlain(n,t))},r.prototype.deserialize=function(n,t,e){var o=JSON.parse(t);return this.plainToInstance(n,o,e)},r.prototype.deserializeArray=function(n,t,e){var o=JSON.parse(t);return this.plainToInstance(n,o,e)},r}();function H(r){return r===void 0&&(r={}),function(n,t){m.addExcludeMetadata({target:n instanceof Function?n:n.constructor,propertyName:t,options:r})}}function Q(r){return r===void 0&&(r={}),function(n,t){m.addExposeMetadata({target:n instanceof Function?n:n.constructor,propertyName:t,options:r})}}function W(r){return function(n,t,e){var o=new I,u=e.value;e.value=function(){for(var s=[],a=0;a<arguments.length;a++)s[a]=arguments[a];var f=u.apply(this,s),l=!!f&&(typeof f=="object"||typeof f=="function")&&typeof f.then=="function";return l?f.then(function(c){return o.instanceToInstance(c,r)}):o.instanceToInstance(f,r)}}}function X(r){return function(n,t,e){var o=new I,u=e.value;e.value=function(){for(var s=[],a=0;a<arguments.length;a++)s[a]=arguments[a];var f=u.apply(this,s),l=!!f&&(typeof f=="object"||typeof f=="function")&&typeof f.then=="function";return l?f.then(function(c){return o.instanceToPlain(c,r)}):o.instanceToPlain(f,r)}}}function Y(r,n){return function(t,e,o){var u=new I,s=o.value;o.value=function(){for(var a=[],f=0;f<arguments.length;f++)a[f]=arguments[f];var l=s.apply(this,a),c=!!l&&(typeof l=="object"||typeof l=="function")&&typeof l.then=="function";return c?l.then(function(y){return u.plainToInstance(r,y,n)}):u.plainToInstance(r,l,n)}}}function Z(r,n){return n===void 0&&(n={}),function(t,e){m.addTransformMetadata({target:t.constructor,propertyName:e,transformFn:r,options:n})}}function V(r,n){return n===void 0&&(n={}),function(t,e){var o=Reflect.getMetadata("design:type",t,e);m.addTypeMetadata({target:t.constructor,propertyName:e,reflectedType:o,typeFunction:r,options:n})}}var x=new I;function nn(r,n){return x.instanceToPlain(r,n)}function tn(r,n){return x.instanceToPlain(r,n)}function en(r,n,t){return x.classToPlainFromExist(r,n,t)}function rn(r,n,t){return x.plainToInstance(r,n,t)}function on(r,n,t){return x.plainToInstance(r,n,t)}function sn(r,n,t){return x.plainToClassFromExist(r,n,t)}function fn(r,n){return x.instanceToInstance(r,n)}function an(r,n,t){return x.classToClassFromExist(r,n,t)}function un(r,n){return x.serialize(r,n)}function pn(r,n,t){return x.deserialize(r,n,t)}function cn(r,n,t){return x.deserializeArray(r,n,t)}const dn=Object.freeze(Object.defineProperty({__proto__:null,ClassTransformer:I,Exclude:H,Expose:Q,Transform:Z,TransformInstanceToInstance:W,TransformInstanceToPlain:X,TransformPlainToInstance:Y,get TransformationType(){return p},Type:V,classToClassFromExist:an,classToPlain:nn,classToPlainFromExist:en,deserialize:pn,deserializeArray:cn,instanceToInstance:fn,instanceToPlain:tn,plainToClass:rn,plainToClassFromExist:sn,plainToInstance:on,serialize:un},Symbol.toStringTag,{value:"Module"})),yn=R(dn);export{R as a,ln as g,yn as r};
