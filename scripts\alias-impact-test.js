#!/usr/bin/env node

/**
 * Resolve Alias Impact Test
 * 
 * This script tests the impact of resolve.alias on bundle size by:
 * 1. Building with full alias configuration (current)
 * 2. Building with no aliases (allowing real dependencies)
 * 3. Building with partial aliases (selective stubbing)
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

const FRONTEND_DIR = path.join(__dirname, '../apps/frontend');
const VITE_CONFIG_PATH = path.join(FRONTEND_DIR, 'vite.config.ts');
const DIST_DIR = path.join(FRONTEND_DIR, 'dist');
const RESULTS_DIR = path.join(__dirname, '../alias-impact-analysis');

// Ensure results directory exists
if (!fs.existsSync(RESULTS_DIR)) {
  fs.mkdirSync(RESULTS_DIR, { recursive: true });
}

console.log('🧪 Resolve Alias Impact Test: Transitive Dependency Analysis');
console.log('============================================================\n');

// Read original config
const originalConfig = fs.readFileSync(VITE_CONFIG_PATH, 'utf8');
fs.writeFileSync(VITE_CONFIG_PATH + '.backup', originalConfig);

function cleanDist() {
  if (fs.existsSync(DIST_DIR)) {
    fs.rmSync(DIST_DIR, { recursive: true, force: true });
  }
}

function getBundleStats(distPath) {
  const stats = {};
  
  if (!fs.existsSync(distPath)) {
    return stats;
  }

  const assetsPath = path.join(distPath, 'assets');
  const directories = [distPath];
  
  if (fs.existsSync(assetsPath)) {
    directories.push(assetsPath);
  }
  
  directories.forEach(dir => {
    const files = fs.readdirSync(dir);
    
    files.forEach(file => {
      const filePath = path.join(dir, file);
      const stat = fs.statSync(filePath);
      
      if (stat.isFile() && (file.endsWith('.js') || file.endsWith('.css'))) {
        stats[file] = {
          size: stat.size,
          sizeKB: Math.round(stat.size / 1024 * 100) / 100
        };
      }
    });
  });

  return stats;
}

function analyzeBundle(distPath, label) {
  console.log(`\n📊 Analyzing ${label}:`);
  console.log('─'.repeat(50));
  
  const stats = getBundleStats(distPath);
  let totalSize = 0;
  
  Object.entries(stats).forEach(([file, info]) => {
    console.log(`  ${file}: ${info.sizeKB} KB`);
    totalSize += info.size;
  });
  
  console.log(`  Total: ${Math.round(totalSize / 1024 * 100) / 100} KB`);
  
  return { stats, totalSize };
}

function buildWithConfig(configModifier, label) {
  console.log(`\n🔨 Building ${label}...`);
  
  // Modify config if needed
  if (configModifier) {
    const modifiedConfig = configModifier(originalConfig);
    fs.writeFileSync(VITE_CONFIG_PATH, modifiedConfig);
  }
  
  // Clean and build
  cleanDist();
  
  try {
    execSync('pnpm nx build frontend', { 
      cwd: path.join(__dirname, '..'), 
      stdio: 'pipe' 
    });
    
    // Copy dist to results for comparison
    const resultPath = path.join(RESULTS_DIR, label.toLowerCase().replace(/\s+/g, '-'));
    if (fs.existsSync(resultPath)) {
      fs.rmSync(resultPath, { recursive: true, force: true });
    }
    fs.cpSync(DIST_DIR, resultPath, { recursive: true });
    
    return analyzeBundle(DIST_DIR, label);
    
  } catch (error) {
    console.error(`❌ Build failed for ${label}:`, error.message);
    return null;
  }
}

// Test scenarios
const tests = [
  {
    label: 'Full Alias Configuration',
    modifier: null // Use current config
  },
  {
    label: 'No Faker Alias Only',
    modifier: (config) => {
      // Remove only faker alias to see its specific impact
      return config.replace(
        /\{\s*find:\s*\/\^@faker-js\\\/faker\$\/,\s*replacement:\s*'[^']*',?\s*\},?/g,
        ''
      );
    }
  },
  {
    label: 'No Backend Aliases',
    modifier: (config) => {
      // Remove all backend dependency aliases
      return config.replace(
        /\/\/ Create virtual modules for backend dependencies[\s\S]*?(?=\],)/,
        '// Backend aliases removed for testing'
      );
    }
  },
  {
    label: 'No Aliases At All',
    modifier: (config) => {
      // Remove entire alias configuration
      return config.replace(
        /alias:\s*\[[^\]]*\],/s,
        'alias: [],'
      );
    }
  }
];

// Run all tests
const results = [];
for (const test of tests) {
  const result = buildWithConfig(test.modifier, test.label);
  if (result) {
    results.push({ label: test.label, data: result });
  }
  
  // Restore original config between tests
  fs.writeFileSync(VITE_CONFIG_PATH, originalConfig);
}

// Generate comparison report
console.log('\n📈 Alias Impact Comparison Report');
console.log('='.repeat(60));

if (results.length > 0) {
  const baseline = results[0];
  
  results.forEach((result, index) => {
    if (index === 0) {
      console.log(`\n${result.label} (baseline):`);
      console.log(`  Total size: ${Math.round(result.data.totalSize / 1024 * 100) / 100} KB`);
    } else {
      const sizeDiff = result.data.totalSize - baseline.data.totalSize;
      const percentDiff = Math.round((sizeDiff / baseline.data.totalSize) * 100 * 100) / 100;
      
      console.log(`\n${result.label}:`);
      console.log(`  Total size: ${Math.round(result.data.totalSize / 1024 * 100) / 100} KB`);
      console.log(`  Difference: ${sizeDiff > 0 ? '+' : ''}${Math.round(sizeDiff / 1024 * 100) / 100} KB (${percentDiff > 0 ? '+' : ''}${percentDiff}%)`);
      
      if (sizeDiff > 10000) { // More than 10KB difference
        console.log(`  🚨 SIGNIFICANT IMPACT: This configuration adds substantial bundle size!`);
      }
    }
  });
}

// Restore original config
fs.writeFileSync(VITE_CONFIG_PATH, originalConfig);
fs.unlinkSync(VITE_CONFIG_PATH + '.backup');

console.log(`\n📁 Bundle files saved to: ${RESULTS_DIR}`);
console.log('\n✅ Alias impact test completed!');
console.log('\n💡 Key insights:');
console.log('   - resolve.alias prevents transitive dependency resolution');
console.log('   - Virtual modules (data:text/javascript) are extremely lightweight');
console.log('   - Each alias can save hundreds of KB by avoiding real dependencies');
console.log('   - The bigger the original dependency, the more alias saves');
