import { CreateUserDto, UserResponseDto, UpdateUserDto } from './common.js';

describe('common DTOs', () => {
  it('should create CreateUserDto instance', () => {
    const dto = new CreateUserDto();
    expect(dto).toBeInstanceOf(CreateUserDto);
  });

  it('should create UserResponseDto instance', () => {
    const dto = new UserResponseDto();
    expect(dto).toBeInstanceOf(UserResponseDto);
  });

  it('should create UpdateUserDto instance', () => {
    const dto = new UpdateUserDto();
    expect(dto).toBeInstanceOf(UpdateUserDto);
  });
});
