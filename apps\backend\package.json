{"name": "@./backend", "version": "0.0.1", "private": true, "dependencies": {"@my-nx/common": "workspace:^0.0.1", "@nestjs/common": "^11.0.0", "@nestjs/core": "^11.0.0", "@nestjs/mapped-types": "^2.1.0", "@nestjs/platform-express": "^11.0.0", "@nestjs/swagger": "^11.2.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.0", "swagger-ui-express": "^5.0.1"}, "nx": {"name": "backend", "targets": {"build": {"executor": "nx:run-commands", "options": {"command": "webpack-cli build", "args": ["node-env=production"]}, "configurations": {"development": {"args": ["node-env=development"]}}}, "serve": {"executor": "nx:run-commands", "defaultConfiguration": "development", "dependsOn": ["build"], "options": {"command": "node apps/backend/dist/main.js", "cwd": "{workspaceRoot}"}, "configurations": {"development": {"command": "node apps/backend/dist/main.js", "cwd": "{workspaceRoot}", "env": {"NODE_ENV": "development"}}, "production": {"command": "node apps/backend/dist/main.js", "cwd": "{workspaceRoot}", "env": {"NODE_ENV": "production"}}}}, "test": {"options": {"passWithNoTests": true}}}}}