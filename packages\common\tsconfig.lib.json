{"compilerOptions": {"baseUrl": ".", "rootDir": "src", "outDir": "dist", "tsBuildInfoFile": "dist/tsconfig.lib.tsbuildinfo", "declaration": true, "emitDeclarationOnly": false, "forceConsistentCasingInFileNames": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "strictPropertyInitialization": false, "types": ["node"], "module": "commonjs", "moduleResolution": "node", "target": "es2021", "composite": true, "declarationMap": true, "importHelpers": true, "isolatedModules": true, "lib": ["es2022"], "noEmitOnError": true, "noFallthroughCasesInSwitch": true, "noImplicitOverride": true, "noImplicitReturns": true, "noUnusedLocals": true, "skipLibCheck": true, "strict": true}, "include": ["src/**/*.ts"], "exclude": ["src/**/*.spec.ts", "src/**/*.test.ts"], "references": []}