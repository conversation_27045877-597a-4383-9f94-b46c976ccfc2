import React from 'react';
// Import only what we actually use
import { UserResponseDto, CreateUserDto } from '@my-nx/common';

// Import heavy utilities but DON'T use them - they should be tree-shaken
import {
  generateComplexFakeData,
  generateFakeEcommerceData,
  generateFakeDatabaseRecords,
  generateFakeAnimalFoodData,
  generateComprehensiveFakeData,
} from '@my-nx/common';

export function TreeShakingTest() {
  // Only use the DTOs
  const createUser = (): CreateUserDto => {
    const dto = new CreateUserDto();
    dto.name = 'Test User';
    dto.email = '<EMAIL>';
    dto.age = 25;
    return dto;
  };

  const createUserResponse = (): UserResponseDto => {
    const dto = new UserResponseDto();
    dto.id = '123e4567-e89b-12d3-a456-426614174000';
    dto.name = 'Test User';
    dto.email = '<EMAIL>';
    dto.age = 25;
    dto.createdAt = new Date();
    return dto;
  };

  // NOTE: We import the heavy utilities above but DON'T call them
  // This tests if tree-shaking properly removes unused imports
  // If tree-shaking works, faker should not be in the bundle

  return (
    <div style={{ padding: '20px', border: '1px solid #ccc', margin: '10px' }}>
      <h3>Tree-Shaking Test Component</h3>
      <p>This component imports heavy utilities but doesn't use them.</p>
      <p>If tree-shaking works, faker should not be in the bundle.</p>

      <div>
        <h4>Used DTOs (should be in bundle):</h4>
        <pre>{JSON.stringify(createUser(), null, 2)}</pre>
        <pre>{JSON.stringify(createUserResponse(), null, 2)}</pre>
      </div>

      <div>
        <h4>Imported but unused (should be tree-shaken):</h4>
        <ul>
          <li>generateComplexFakeData</li>
          <li>generateFakeEcommerceData</li>
          <li>generateFakeDatabaseRecords</li>
          <li>generateFakeAnimalFoodData</li>
          <li>generateComprehensiveFakeData</li>
        </ul>
        <p>
          <strong>
            These functions and faker should NOT be in the bundle!
          </strong>
        </p>
      </div>
    </div>
  );
}
