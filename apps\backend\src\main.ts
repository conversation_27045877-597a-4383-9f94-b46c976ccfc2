/**
 * This is not a production server yet!
 * This is only a minimal backend to get started.
 */

import { Logger, ValidationPipe } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { AppModule } from './app/app.module';
import { NestExpressApplication } from '@nestjs/platform-express';
import * as swaggerUi from 'swagger-ui-express';

async function bootstrap() {
  const app = await NestFactory.create<NestExpressApplication>(AppModule);

  // Enable validation
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
    })
  );

  // Enable CORS for frontend
  app.enableCors({
    origin: 'http://localhost:4200',
    credentials: true,
  });

  // Setup Swagger FIRST (before global prefix)
  const config = new DocumentBuilder()
    .setTitle('My NX API')
    .setDescription('API documentation for the NX monorepo backend')
    .setVersion('1.0')
    .build();
  const document = SwaggerModule.createDocument(app, config);

  // Use default NestJS Swagger setup (assets will be redirected to CDN)
  SwaggerModule.setup('docs', app, document, {
    swaggerOptions: {
      persistAuthorization: true,
    },
    customSiteTitle: 'My NX API Documentation',
  });

  const globalPrefix = 'api';
  app.setGlobalPrefix(globalPrefix, {
    exclude: ['/docs'],
  });

  // Serve Swagger UI static assets from CDN by creating proxy routes
  const expressApp = app.getHttpAdapter().getInstance();

  expressApp.get('/docs/swagger-ui.css', (req, res) => {
    res.redirect('https://unpkg.com/swagger-ui-dist@4.15.5/swagger-ui.css');
  });

  expressApp.get('/docs/swagger-ui-bundle.js', (req, res) => {
    res.redirect(
      'https://unpkg.com/swagger-ui-dist@4.15.5/swagger-ui-bundle.js'
    );
  });

  expressApp.get('/docs/swagger-ui-standalone-preset.js', (req, res) => {
    res.redirect(
      'https://unpkg.com/swagger-ui-dist@4.15.5/swagger-ui-standalone-preset.js'
    );
  });

  // The default swagger-ui-init.js is served by NestJS, so our redirect approach is working
  // The CSS and JS files will be redirected to CDN, which should resolve the 404 errors
  const port = process.env.PORT || 3000;
  await app.listen(port);
  Logger.log(
    `🚀 Application is running on: http://localhost:${port}/${globalPrefix}`
  );
  Logger.log(
    `📚 Swagger documentation available at: http://localhost:${port}/docs`
  );
}

bootstrap();
