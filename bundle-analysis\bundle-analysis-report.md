# Bundle Size Analysis Report

Generated: 2025-07-17T13:22:07.579Z

## Test Overview

This test compares bundle sizes with different Vite configurations to demonstrate the impact of `namedExports` on tree-shaking effectiveness.

## Test Scenarios

1. **With namedExports**: Current configuration with explicit named exports
2. **Without namedExports**: Same config but without the namedExports specification
3. **No Tree-shaking**: Tree-shaking completely disabled for comparison

## Results

### With namedExports

Total bundle size: **279.98 KB**

| File | Size (KB) |
|------|----------|
| class-transformer-CJFEAE5x.js | 16.92 |
| index-B8j6v-bA.js | 16.69 |
| index-tn0RQdqM.css | 0 |
| vendor-BiO7oy6w.js | 246.36 |

### Without namedExports

Total bundle size: **279.98 KB**

| File | Size (KB) |
|------|----------|
| class-transformer-CJFEAE5x.js | 16.92 |
| index-B8j6v-bA.js | 16.69 |
| index-tn0RQdqM.css | 0 |
| vendor-BiO7oy6w.js | 246.36 |

### No Tree-shaking

Total bundle size: **284.37 KB**

| File | Size (KB) |
|------|----------|
| class-transformer-B6NQvfWW.js | 16.92 |
| index-D9zNuQB4.js | 16.71 |
| index-tn0RQdqM.css | 0 |
| vendor-CDdeawtU.js | 250.74 |

## Analysis

**Without namedExports vs With namedExports:**
- Size difference: 0 KB
- Percentage change: 0%

**No Tree-shaking vs With namedExports:**
- Size difference: **** KB
- Percentage change: *****%

## Conclusion

The `namedExports` configuration in Vite's `commonjsOptions` helps Rollup understand which exports are available from CommonJS modules like `@my-nx/common`. This enables:

1. **Better tree-shaking**: Rollup can eliminate unused exports more effectively
2. **Smaller bundles**: Only imported functions/classes are included
3. **Cleaner imports**: Named imports work reliably with CommonJS modules

## Files Generated

- Bundle analysis results are saved in: `C:\Users\<USER>\projects\my-nx\bundle-analysis`
- Each test scenario has its own directory with the built files
- Bundle visualization is available in `dist/bundle-analysis.html` after each build
