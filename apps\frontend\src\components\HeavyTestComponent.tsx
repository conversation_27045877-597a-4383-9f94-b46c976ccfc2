import React from 'react';
// Import only what we actually use
import { UserResponseDto, CreateUserDto } from '@my-nx/common';

// Import ALL heavy utilities and actually USE them - this should increase bundle size significantly
import {
  generateComplexFakeData,
  generateFakeEcommerceData,
  generateFakeDatabaseRecords,
  generateFakeAnimalFoodData,
  generateComprehensiveFakeData,
} from '@my-nx/common';

export function HeavyTestComponent() {
  // Actually use all the heavy functions to force them into the bundle
  const complexData = generateComplexFakeData(1);
  const ecommerceData = generateFakeEcommerceData(1);
  const dbRecords = generateFakeDatabaseRecords(1);
  const animalFoodData = generateFakeAnimalFoodData(1);
  const comprehensiveData = generateComprehensiveFakeData(1);

  const sampleUser: UserResponseDto = {
    id: '123e4567-e89b-12d3-a456-426614174000',
    name: 'Test User',
    email: '<EMAIL>',
    age: 25,
    createdAt: new Date(),
  };

  return (
    <div style={{ padding: '20px', border: '1px solid #red', margin: '10px' }}>
      <h3>Heavy Test Component (Uses ALL imports)</h3>
      <p>This component actually USES all the heavy utilities from @my-nx/common.</p>
      <p>This should result in a much larger bundle size because faker is included.</p>

      <div>
        <h4>Used DTOs:</h4>
        <pre style={{ fontSize: '10px' }}>{JSON.stringify(sampleUser, null, 2)}</pre>
      </div>

      <div>
        <h4>Heavy Data Generated (all functions used):</h4>
        <div style={{ maxHeight: '200px', overflow: 'auto', fontSize: '10px' }}>
          <p>Complex: {complexData.users.length} users</p>
          <p>Ecommerce: {ecommerceData.products.length} products</p>
          <p>Database: {dbRecords.records.length} records</p>
          <p>Animal/Food: {animalFoodData.data.length} entries</p>
          <p>Comprehensive: {comprehensiveData.data.length} entries</p>
        </div>
        <p style={{ color: 'red', fontSize: '12px' }}>
          <strong>All heavy functions are called, so faker WILL be in the bundle!</strong>
        </p>
      </div>
    </div>
  );
}
