/// <reference types='vitest' />
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { visualizer } from 'rollup-plugin-visualizer';

export default defineConfig(() => ({
  root: __dirname,
  cacheDir: '../../node_modules/.vite/apps/frontend',
  server: {
    port: 4200,
    host: 'localhost',
  },
  preview: {
    port: 4200,
    host: 'localhost',
  },
  define: {
    // Replace global require function for problematic modules
    global: 'globalThis',
    'process.env.NODE_ENV': '"production"',
  },

  plugins: [
    react(),
    visualizer({
      filename: 'dist/bundle-analysis.html',
      open: false,
      gzipSize: true,
      brotliSize: true,
    }),
    // Backend dependency stubbing plugin - works better in dev mode
    {
      name: 'backend-dependency-stubs',
      enforce: 'pre',
      resolveId(id) {
        const backendDeps = [
          'class-validator',
          '@faker-js/faker',
          'reflect-metadata',
          '@nestjs/swagger',
          '@nestjs/common',
          '@nestjs/core',
          '@nestjs/platform-express',
          '@nestjs/mapped-types',
        ];

        if (backendDeps.includes(id)) {
          return id; // Handle in load hook
        }
        return null;
      },
      load(id) {
        if (id === 'class-validator') {
          return `
            // Dynamic class-validator stub - handles any decorator
            const createDecorator = () => () => () => {};
            const createValidator = () => Promise.resolve([]);

            // Export all common decorators explicitly
            export const validate = createValidator;
            export const IsString = createDecorator;
            export const IsEmail = createDecorator;
            export const IsOptional = createDecorator;
            export const IsNumber = createDecorator;
            export const Min = createDecorator;
            export const IsUUID = createDecorator;
            export const IsDateString = createDecorator;
            export const IsArray = createDecorator;
            export const ValidateNested = createDecorator;
            export const IsCustomThing = createDecorator;

            // Create a proxy for any other exports
            const validatorProxy = new Proxy({}, {
              get(target, prop) {
                if (prop === 'validate') return createValidator;
                if (prop === 'default') return validatorProxy;
                if (prop === '__esModule') return true;
                return createDecorator; // Any other property returns a decorator
              }
            });

            export default validatorProxy;

            // CommonJS compatibility
            module.exports = new Proxy(validatorProxy, {
              get(target, prop) {
                if (prop === 'validate') return createValidator;
                if (prop === 'IsString') return createDecorator;
                if (prop === 'IsEmail') return createDecorator;
                if (prop === 'IsOptional') return createDecorator;
                if (prop === 'IsNumber') return createDecorator;
                if (prop === 'Min') return createDecorator;
                if (prop === 'IsUUID') return createDecorator;
                if (prop === 'IsDateString') return createDecorator;
                if (prop === 'IsArray') return createDecorator;
                if (prop === 'ValidateNested') return createDecorator;
                if (prop === 'IsCustomThing') return createDecorator;
                if (prop === '__esModule') return true;
                if (prop === 'default') return validatorProxy;
                return createDecorator; // Any other export
              }
            });
          `;
        }

        if (id === '@faker-js/faker') {
          return `
            // Dynamic faker stub - handles any property/method access
            const createFakerProxy = (path = []) => {
              return new Proxy(() => {
                // If called as a function, return a reasonable default
                if (path.includes('uuid')) return 'fake-uuid-' + Math.random().toString(36).substr(2, 9);
                if (path.includes('email')) return '<EMAIL>';
                if (path.includes('name') || path.includes('firstName')) return 'John';
                if (path.includes('number') || path.includes('int')) return 42;
                if (path.includes('boolean')) return true;
                if (path.includes('date')) return new Date();
                return 'fake-data';
              }, {
                get(target, prop) {
                  if (prop === 'toString') return () => 'fake-data';
                  if (prop === 'valueOf') return () => 'fake-data';
                  if (prop === Symbol.toPrimitive) return () => 'fake-data';
                  // Return another proxy for chaining (faker.person.firstName)
                  return createFakerProxy([...path, prop]);
                }
              });
            };

            export const faker = createFakerProxy();
            export default { faker };

            // CommonJS compatibility
            module.exports = { faker, default: { faker } };
          `;
        }

        if (id === 'reflect-metadata') {
          return `
            // Reflect-metadata stub for frontend
            export default {};
          `;
        }

        if (id === '@nestjs/swagger') {
          return `
            // Dynamic NestJS Swagger stub - handles any decorator
            const createDecorator = () => () => () => {};

            // Export common swagger decorators explicitly
            export const ApiProperty = createDecorator;
            export const ApiResponse = createDecorator;
            export const ApiTags = createDecorator;
            export const ApiOperation = createDecorator;
            export const ApiParam = createDecorator;
            export const ApiQuery = createDecorator;
            export const ApiBody = createDecorator;
            export const ApiHeader = createDecorator;

            // Create proxy for any other exports
            const nestProxy = new Proxy({}, {
              get(target, prop) {
                if (prop === 'default') return nestProxy;
                if (prop === '__esModule') return true;
                return createDecorator; // Any other export returns a decorator
              }
            });

            export default nestProxy;

            // CommonJS compatibility
            module.exports = new Proxy(nestProxy, {
              get(target, prop) {
                if (prop === 'ApiProperty') return createDecorator;
                if (prop === 'ApiResponse') return createDecorator;
                if (prop === 'ApiTags') return createDecorator;
                if (prop === 'ApiOperation') return createDecorator;
                if (prop === 'ApiParam') return createDecorator;
                if (prop === 'ApiQuery') return createDecorator;
                if (prop === 'ApiBody') return createDecorator;
                if (prop === 'ApiHeader') return createDecorator;
                if (prop === '__esModule') return true;
                if (prop === 'default') return nestProxy;
                return createDecorator; // Any other export
              }
            });
          `;
        }

        if (
          id === '@nestjs/common' ||
          id === '@nestjs/core' ||
          id === '@nestjs/platform-express' ||
          id === '@nestjs/mapped-types'
        ) {
          return `
            // Dynamic NestJS stub - handles any export
            const nestProxy = new Proxy({}, {
              get(target, prop) {
                if (prop === 'default') return nestProxy;
                if (prop === '__esModule') return true;
                // Return decorators, classes, or functions as needed
                return () => () => {};
              }
            });

            export default nestProxy;
            module.exports = nestProxy;
          `;
        }

        return null;
      },
    },
  ],
  resolve: {
    alias: [
      // Fix for @nestjs/mapped-types trying to import class-transformer/storage
      { find: 'class-transformer/storage', replacement: 'class-transformer' },
      // Note: Backend dependencies are handled by the custom plugin above
    ],
  },
  optimizeDeps: {
    include: ['class-transformer', 'reflect-metadata'],
    exclude: [
      // Backend dependencies that are stubbed by our plugin
      'class-validator',
      '@faker-js/faker',
      '@nestjs/swagger',
      '@nestjs/common',
      '@nestjs/core',
      '@nestjs/platform-express',
      '@nestjs/mapped-types',
      // Keep common package out of pre-bundling for better dev experience
      '@my-nx/common',
    ],
  },
  build: {
    outDir: './dist',
    emptyOutDir: true,
    reportCompressedSize: true,
    commonjsOptions: {
      transformMixedEsModules: true,
      include: [/node_modules/, /packages\/common/],
    },
    rollupOptions: {
      external: [],
      treeshake: {
        moduleSideEffects: false,
        propertyReadSideEffects: false,
        tryCatchDeoptimization: false,
      },
      output: {
        manualChunks: (id) => {
          // Separate chunks for debugging
          if (id.includes('@faker-js/faker')) {
            return 'faker';
          }
          if (id.includes('@my-nx/common')) {
            return 'common';
          }
          if (id.includes('class-transformer')) {
            return 'class-transformer';
          }
          if (id.includes('nest')) {
            return 'nest';
          }
          if (id.includes('node_modules')) {
            return 'vendor';
          }
          return undefined;
        },
      },
    },
  },
  test: {
    watch: false,
    globals: true,
    environment: 'jsdom',
    include: ['{src,tests}/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}'],
    reporters: ['default'],
    coverage: {
      reportsDirectory: './test-output/vitest/coverage',
      provider: 'v8' as const,
    },
  },
}));
