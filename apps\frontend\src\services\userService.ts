import axios from 'axios';
import { CreateUserDto, UserResponseDto, UpdateUserDto } from '@my-nx/common';

const API_BASE_URL = 'http://localhost:3000/api';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

export class UserService {
  static async getUsers(): Promise<UserResponseDto[]> {
    const response = await api.get<UserResponseDto[]>('/users');
    return response.data;
  }

  static async getUser(id: string): Promise<UserResponseDto> {
    const response = await api.get<UserResponseDto>(`/users/${id}`);
    return response.data;
  }

  static async createUser(userData: CreateUserDto): Promise<UserResponseDto> {
    const response = await api.post<UserResponseDto>('/users', userData);
    return response.data;
  }

  static async updateUser(
    id: string,
    userData: UpdateUserDto
  ): Promise<UserResponseDto> {
    const response = await api.put<UserResponseDto>(`/users/${id}`, userData);
    return response.data;
  }
}
