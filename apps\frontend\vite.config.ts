/// <reference types='vitest' />
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { visualizer } from 'rollup-plugin-visualizer';

export default defineConfig(() => ({
  root: __dirname,
  cacheDir: '../../node_modules/.vite/apps/frontend',
  server: {
    port: 4200,
    host: 'localhost',
  },
  preview: {
    port: 4200,
    host: 'localhost',
  },
  define: {
    // Replace global require function for problematic modules
    global: 'globalThis',
    'process.env.NODE_ENV': '"production"',
  },

  plugins: [
    react(),
    visualizer({
      filename: 'dist/bundle-analysis.html',
      open: false,
      gzipSize: true,
      brotliSize: true,
    }),
    // Final attempt: Direct module replacement with exact CommonJS structure
    {
      name: 'frontend-direct-replacement',
      resolveId(id) {
        if (
          [
            'class-validator',
            '@faker-js/faker',
            'reflect-metadata',
            '@nestjs/swagger',
          ].includes(id)
        ) {
          return id; // Don't mark as external, handle in load
        }
        return null;
      },
      load(id) {
        if (id === 'class-validator') {
          // Return exactly what the CommonJS interop expects
          return `
            const noop = () => () => {};
            const validate = () => Promise.resolve([]);
            const IsString = noop;
            const IsEmail = noop;
            const IsOptional = noop;
            const IsNumber = noop;
            const Min = noop;
            const IsUUID = noop;
            const IsDateString = noop;

            // Export exactly as CommonJS module
            module.exports = {
              __esModule: true,
              validate,
              IsString,
              IsEmail,
              IsOptional,
              IsNumber,
              Min,
              IsUUID,
              IsDateString,
              default: {
                validate,
                IsString,
                IsEmail,
                IsOptional,
                IsNumber,
                Min,
                IsUUID,
                IsDateString
              }
            };

            // Also provide ES6 exports for compatibility
            export { validate, IsString, IsEmail, IsOptional, IsNumber, Min, IsUUID, IsDateString };
            export default {
              validate,
              IsString,
              IsEmail,
              IsOptional,
              IsNumber,
              Min,
              IsUUID,
              IsDateString
            };
          `;
        }

        if (id === '@faker-js/faker') {
          return `
            const createProxy = () => new Proxy({}, { get: () => createProxy() });
            const faker = createProxy();

            module.exports = {
              __esModule: true,
              faker,
              default: { faker }
            };

            export { faker };
            export default { faker };
          `;
        }

        if (id === 'reflect-metadata') {
          return `
            module.exports = { __esModule: true, default: {} };
            export default {};
          `;
        }

        if (id === '@nestjs/swagger') {
          return `
            const ApiProperty = () => () => {};

            module.exports = {
              __esModule: true,
              ApiProperty,
              default: { ApiProperty }
            };

            export { ApiProperty };
            export default { ApiProperty };
          `;
        }

        return null;
      },
    },
  ],
  resolve: {
    alias: [
      // Fix for @nestjs/mapped-types trying to import class-transformer/storage
      { find: 'class-transformer/storage', replacement: 'class-transformer' },
      // Create virtual modules for backend dependencies
      {
        find: /^class-validator$/,
        replacement:
          'data:text/javascript,export const validate = () => Promise.resolve([]);export default {};',
      },
      {
        find: /^@faker-js\/faker$/,
        replacement:
          'data:text/javascript,export const faker = {};export default {};',
      },
      {
        find: /^@nestjs\/swagger$/,
        replacement:
          'data:text/javascript,export const ApiProperty = () => () => {};export default {};',
      },
      // {
      //   find: /^@nestjs\/common$/,
      //   replacement: 'data:text/javascript,export default {};',
      // },
      // {
      //   find: /^@nestjs\/core$/,
      //   replacement: 'data:text/javascript,export default {};',
      // },
      // {
      //   find: /^@nestjs\/platform-express$/,
      //   replacement: 'data:text/javascript,export default {};',
      // },
      // {
      //   find: /^@nestjs\/mapped-types$/,
      //   replacement: 'data:text/javascript,export default {};',
      // },
      // {
      //   find: /^reflect-metadata$/,
      //   replacement: 'data:text/javascript,export default {};',
      // },
    ],
  },
  optimizeDeps: {
    include: ['class-transformer', 'reflect-metadata'],
    exclude: [
      '@nestjs/mapped-types',
      '@nestjs/swagger',
      '@nestjs/common',
      '@nestjs/core',
      '@nestjs/platform-express',
      'class-validator',
      'swagger-ui-express',
      'express',
      'rxjs',
      '@my-nx/common',
    ],
  },
  ssr: {
    noExternal: ['@my-nx/common'],
  },
  build: {
    outDir: './dist',
    emptyOutDir: true,
    reportCompressedSize: true,
    commonjsOptions: {
      transformMixedEsModules: true,
      include: [/node_modules/, /packages\/common/],
    },
    rollupOptions: {
      external: [],
      treeshake: {
        moduleSideEffects: false,
        propertyReadSideEffects: false,
        tryCatchDeoptimization: false,
      },
      output: {
        manualChunks: (id) => {
          // Separate chunks for debugging
          if (id.includes('@faker-js/faker')) {
            return 'faker';
          }
          if (id.includes('@my-nx/common')) {
            return 'common';
          }
          if (id.includes('class-transformer')) {
            return 'class-transformer';
          }
          if (id.includes('nest')) {
            return 'nest';
          }
          if (id.includes('node_modules')) {
            return 'vendor';
          }
          return undefined;
        },
      },
    },
  },
  test: {
    watch: false,
    globals: true,
    environment: 'jsdom',
    include: ['{src,tests}/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}'],
    reporters: ['default'],
    coverage: {
      reportsDirectory: './test-output/vitest/coverage',
      provider: 'v8' as const,
    },
  },
}));
